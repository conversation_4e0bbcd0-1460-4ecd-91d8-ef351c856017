#!/bin/bash

# Evidence DuckDB Workflow - Project Cleanup Script
echo "🧹 Starting Evidence DuckDB Workflow project cleanup..."

# Create organized directory structure
echo "📁 Creating organized directory structure..."

# Archive the original broken attempts
mkdir -p archive/original-attempts
echo "Moving broken attempts to archive..."

# Archive development artifacts  
mkdir -p archive/development-artifacts
mv node_modules archive/development-artifacts/ 2>/dev/null || echo "node_modules already moved"
mv package*.json archive/development-artifacts/ 2>/dev/null || echo "package files already moved"
mv webpack.config.js archive/development-artifacts/ 2>/dev/null || echo "webpack.config.js already moved"

# Keep important documentation and configs
mkdir -p docs
mv README-DDX.md docs/ 2>/dev/null || echo "README-DDX.md already moved"
mv build-ddx.sh docs/ 2>/dev/null || echo "build-ddx.sh already moved"
mv Dockerfile docs/ 2>/dev/null || echo "Dockerfile already moved"
mv ddx-template.html docs/ 2>/dev/null || echo "ddx-template.html already moved"
mv serve-ddx.js docs/ 2>/dev/null || echo "serve-ddx.js already moved"

echo ""
echo "✅ Project Cleanup Summary:"
echo "├── domo-evidence-clean/     ✅ WORKING Domo DDX app"
echo "├── archive/                 📦 Old broken attempts"
echo "├── docs/                    📚 Documentation and configs"
echo "├── demo/                    📖 Working demo reference"
echo "└── evidence-project*/       📖 Original Evidence builds"
echo ""
echo "🎯 Next Steps:"
echo "1. cd domo-evidence-clean"
echo "2. domo dev"
echo "3. Continue development from the working base"
echo ""
echo "✨ Cleanup complete! Your project is now organized and working."
