<!doctype html><html lang="en"><head><meta charset="utf-8"/><link rel="icon" href="favicon.ico" sizes="32x32"/><link rel="icon" href="icon.svg" type="image/svg+xml"/><link rel="apple-touch-icon" href="apple-touch-icon.png"/><link rel="manifest" href="manifest.webmanifest"/><meta name="viewport" content="width=device-width,initial-scale=1"/><script>try{const e=localStorage.getItem("evidence-theme")??"system",t=window.matchMedia("(prefers-color-scheme: dark)").matches,c="system"===e?t?"dark":"light":e;document.documentElement.classList.add(`theme-${c}`)}catch(e){}</script><title>Domo Data Loader</title><meta property="og:title" content="Domo Data Loader"><meta name="twitter:title" content="Domo Data Loader"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content="@evidence_dev"><link href="styles.css" rel="stylesheet"><script src="app.js"></script></head><body><script></script><div><div class="z-[1] fixed right-0 bottom-0 mx-10 my-6 w-80"></div><div data-sveltekit-preload-data="hover" class="antialiased"><header class="fixed w-full top-0 z-40 flex h-12 shrink-0 justify-start items-center gap-x-4 border-b border-base-300/50 bg-base-100/90 backdrop-blur print:hidden"><div class="max-w-7xl mx-auto px-5 sm:px-6 md:px-12 flex flex-1 items-center justify-between" style="max-width:undefinedpx"><div class="flex gap-x-4 items-center"><button type="button" class="text-base-content hover:bg-base-200 rounded-lg p-1 -ml-1 transition-all duration-500 md:hidden"><span class="sr-only">Open sidebar</span> <svg class="w-5 h-5" width="100%" height="100%" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M4 6l16 0"></path><path d="M4 12l16 0"></path><path d="M4 18l16 0"></path></svg></button> <a href="/" class="text-sm font-bold text-base-content hidden md:block"><img src="_app/_app/immutable/assets/wordmark-black.rfl-FBgf.png" alt="Home" class="h-5 aspect-auto block dark:hidden" href="/"> <img src="_app/_app/immutable/assets/wordmark-white.C8ZS96Ri.png" alt="Home" class="h-5 aspect-auto hidden dark:block" href="/"></a></div><div class="flex gap-2 text-sm items-center"><div class="flex gap-2 items-center"></div><div class="relative"><button type="button" tabindex="0" aria-controls="kmd2uclcUd" aria-expanded="false" data-state="closed" id="ocPXKmnF80" data-melt-dropdown-menu-trigger="" data-menu-trigger="" class="inline-flex items-center justify-center font-medium whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-base-content-muted disabled:pointer-events-none disabled:opacity-50 hover:text-base-content h-8 rounded-md text-xs px-1 hover:bg-base-200 shadow-base-200" aria-label="Menu" data-button-root=""><svg class="h-6 w-6" width="100%" height="100%" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M5 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path><path d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path><path d="M19 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path></svg></button></div></div></div></header><div class="max-w-7xl print:w-[650px] print:md:w-[841px] mx-auto print:md:px-0 print:px-0 px-6 sm:px-8 md:px-12 flex justify-start" style="max-width:undefinedpx"><div class="print:hidden"><aside class="w-48 flex-none hidden md:flex"><div class="hidden: md:block fixed w-48 top-20 bottom-8 overflow-y-auto flex-1 text-sm pretty-scrollbar"><div class="flex flex-col pb-6"><a class="sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize hover:underline text-base-heading" href="/">Home</a></div></div><div class="fixed bottom-0 text-xs py-2"><a href="https://www.evidence.dev" class="bg-gradient-to-r inline-block antialiased font-medium">Built with Evidence</a></div></aside></div><main class="md:pl-8 md:pr-8 mt-16 sm:mt-20 flex-grow overflow-x-hidden print:px-0 print:mt-8"><div class="print:hidden"><div class="flex items-start mt-0 whitespace-nowrap overflow-auto"><div class="inline-flex items-center text-sm capitalize gap-1 text-base-content-muted mb-2 sm:mb-4"><a href="/" class="hover:underline">Home</a></div></div></div><article id="evidence-main-article" class="select-text markdown pb-10"><h1 class="title">Domo Data Loader</h1><p>Load datasets from Domo into DuckDB for analysis.</p><div id="workflow-picker-section" class="workflow-picker-section"><div class="workflow-header"><h3>🔄 Domo Dataset Workflow</h3><p>Select and load Domo datasets into DuckDB for analysis with Evidence</p></div><div id="domo-workflow-picker" class="workflow-picker"><div class="workflow-step"><label for="dataset-search">Search Datasets:</label> <input id="dataset-search" placeholder="Type to search datasets..." class="dataset-search"><div class="dataset-stats" id="dataset-stats"><span id="dataset-count">Loading datasets...</span></div></div><div class="workflow-step"><label for="dataset-selector">Select Dataset:</label> <select id="dataset-selector" class="dataset-dropdown"><option value="">Choose a dataset...</option></select></div><div id="dataset-preview" class="dataset-preview" style="display:none"><h4>📊 Dataset Information</h4><div id="preview-content" class="preview-content"><div id="dataset-info" class="dataset-info-grid"></div><div class="dataset-tabs"><button class="tab-button active" data-tab="schema">Schema</button> <button class="tab-button" data-tab="sample">Sample Data</button> <button class="tab-button" data-tab="metadata">Metadata</button></div><div id="schema-tab" class="tab-content active"><div id="schema-table" class="schema-table"></div></div><div id="sample-tab" class="tab-content"><div class="preview-actions"><button id="preview-btn" class="btn btn-secondary">🔍 Load Sample Data</button> <span class="preview-note">Shows first 10 rows</span></div><div id="data-preview" class="data-preview" style="display:none"></div></div><div id="metadata-tab" class="tab-content"><div id="dataset-metadata" class="dataset-metadata"></div></div></div></div><div id="loading-config" class="workflow-step" style="display:none"><h4>⚙️ Loading Configuration</h4><div class="config-grid"><div class="config-item"><label for="table-name">Table Name in DuckDB:</label> <input id="table-name" placeholder="Enter table name"/> <small class="field-help">Use lowercase letters, numbers, and underscores only</small></div><div class="config-item"><label for="refresh-mode">Refresh Mode:</label> <select id="refresh-mode"><option value="replace">Replace existing data</option><option value="append">Append to existing data</option></select> <small class="field-help">Choose how to handle existing data</small></div><div class="config-item"><label for="row-limit">Row Limit (optional):</label> <input id="row-limit" type="number" placeholder="Leave empty for all rows" min="1" max="1000000"/> <small class="field-help">Limit rows for testing (max 1M)</small></div><div class="config-item"><label><input type="checkbox" id="create-index" checked="checked"/> Create indexes for better performance</label></div></div></div><div id="workflow-actions" class="workflow-actions" style="display:none"><div class="action-buttons"><button id="validate-config-btn" class="btn btn-secondary">✅ Validate Configuration</button> <button id="load-dataset-btn" class="btn btn-primary">📊 Load Dataset into DuckDB</button></div><div id="validation-results" class="validation-results" style="display:none"></div></div><div id="loading-status" class="loading-status" style="display:none"><div class="loading-spinner"></div><p id="loading-message">Loading...</p><div class="progress-bar"><div class="progress-fill" id="progress-fill"></div></div></div></div></div><div id="loaded-datasets-section" style="display:none"><h2 class="markdown">📚 Loaded Datasets</h2><div class="loaded-datasets-header"><p>Datasets currently available in DuckDB for analysis:</p><button id="refresh-loaded-btn" class="btn btn-secondary btn-small">🔄 Refresh</button></div><div id="loaded-datasets-list" class="loaded-datasets-grid"></div></div><div id="query-section" style="display:none"><h2 class="markdown">🔍 Query Your Data</h2><div class="query-interface"><div class="query-input"><label for="sql-query">SQL Query:</label> <textarea id="sql-query" placeholder="SELECT * FROM your_table_name LIMIT 10;" rows="4"></textarea><div class="query-actions"><button id="run-query-btn" class="btn btn-primary">▶️ Run Query</button> <button id="clear-query-btn" class="btn btn-secondary">🗑️ Clear</button></div></div><div id="query-results" class="query-results" style="display:none"></div></div></div></article></main><div class="print:hidden"><aside class="hidden lg:block w-48"><div class="fixed w-48 top-20 bottom-20 pl-4 px-3 overflow-auto pretty-scrollbar"></div></aside></div></div></div><div aria-disabled id="__evidence_project_splash" data-test-id="__evidence_project_splash" style="visibility:hidden"><svg width="100" height="100" viewBox="-8 -8 588 588" xmlns="http://www.w3.org/2000/svg"><path d="M7.19462e-05 74.3583C109.309 74.3583 195.795 86.2578 286.834 37.825C377.872 -10.6077 466.416 1.29174 573.667 1.29175L573.667 126.549C466.416 126.549 377.373 114.91 286.834 163.082C196.294 211.254 109.309 199.615 6.11417e-05 199.615L7.19462e-05 74.3583Z" class="draw-path"/><path d="M573.669 499.31C464.36 499.31 377.874 487.411 286.835 535.843C195.797 584.276 107.252 572.377 0.0014801 572.377V447.12C107.252 447.12 196.295 458.758 286.835 410.586C377.375 362.415 464.36 374.053 573.669 374.053V499.31Z" class="draw-path"/><path d="M452.896 186.499C395.028 187.686 341.581 194.947 286.835 224.074C211.396 264.212 136.995 262.826 52.2355 261.247C35.2696 260.931 17.8887 260.608 0.0014801 260.608V385.865C18.1032 385.865 35.6721 386.204 52.81 386.534C137.212 388.162 211.162 389.589 286.835 349.331C341.838 320.07 395.18 312.831 452.896 311.685V186.499Z" class="draw-path"/></svg></div></div><script>function toggleWorkflowPicker(){const e=document.getElementById("workflow-picker-section");"none"===e.style.display?e.style.display="block":e.style.display="none"}</script><style>#__evidence_project_splash{position:fixed;top:0;left:0;width:100vw;height:100vh;background-color:#fff;display:flex;justify-content:center;align-items:center;z-index:9999}.theme-dark #__evidence_project_splash{background-color:#000}.draw-path{fill:#000000;animation:blinking-logo 2s;animation-fill-mode:both;animation-iteration-count:infinite;animation-timing-function:ease-in-out}.theme-dark .draw-path{fill:#ffffff}@keyframes blinking-logo{0%{fill-opacity:1}50%{fill-opacity:0.2}100%{fill-opacity:1}}.dev-banner{margin:20px 0;padding:20px;border:2px solid #f59e0b;border-radius:12px;background:linear-gradient(135deg,#fffbeb 0,#fef3c7 100%);color:#92400e;text-align:center}.dev-banner h3{margin:0 0 10px 0;color:#d97706}.btn-primary,.btn-secondary{display:inline-block;padding:12px 24px;border:none;border-radius:8px;text-decoration:none;font-weight:600;font-size:14px;cursor:pointer;transition:all .2s}.btn-primary{background-color:#2563eb;color:#fff}.btn-primary:hover{background-color:#1d4ed8;text-decoration:none;color:#fff}.btn-secondary{background-color:#f3f4f6;color:#374151;border:1px solid #d1d5db}.btn-secondary:hover{background-color:#e5e7eb;text-decoration:none;color:#374151}.next-steps{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px;margin:30px 0}.step{padding:20px;border:1px solid #e5e7eb;border-radius:8px;background-color:#fafafa}.step h4{margin:0 0 10px 0;color:#1f2937;font-size:16px}.step p{margin:0;color:#6b7280;line-height:1.5}.workflow-toggle{margin:30px 0;text-align:center}.workflow-toggle button{margin:0 10px}.workflow-picker-section{margin:30px 0;padding:30px;border:2px solid #e5e7eb;border-radius:12px;background-color:#f9fafb}.workflow-header{text-align:center;margin-bottom:30px}.workflow-header h3{margin:0 0 10px 0;color:#1f2937;font-size:24px}.workflow-header p{margin:0;color:#6b7280;font-size:16px}.dataset-search{width:100%;padding:12px 16px;border:2px solid #d1d5db;border-radius:8px;font-size:16px;background-color:#fff;transition:border-color .2s}.dataset-search:focus{outline:0;border-color:#3b82f6;box-shadow:0 0 0 3px rgba(59,130,246,.1)}.dataset-stats{margin-top:8px;font-size:14px;color:#6b7280}.dataset-tabs{display:flex;border-bottom:2px solid #e5e7eb;margin:20px 0}.tab-button{padding:12px 20px;border:none;background:0 0;cursor:pointer;font-weight:500;color:#6b7280;border-bottom:2px solid transparent;transition:all .2s}.tab-button.active{color:#3b82f6;border-bottom-color:#3b82f6}.tab-button:hover{color:#3b82f6}.tab-content{display:none}.tab-content.active{display:block}.dataset-info-grid{margin-bottom:20px}.info-card{background:#fff;border-radius:8px;padding:20px;border:1px solid #e5e7eb}.info-card h5{margin:0 0 10px 0;color:#1f2937;font-size:18px}.description{color:#6b7280;margin-bottom:15px;line-height:1.5}.info-stats{display:grid;grid-template-columns:repeat(auto-fit,minmax(150px,1fr));gap:15px;margin-bottom:15px}.stat{display:flex;flex-direction:column}.stat .label{font-size:12px;color:#9ca3af;text-transform:uppercase;font-weight:600;margin-bottom:4px}.stat .value{font-size:16px;color:#1f2937;font-weight:500}.tags{display:flex;flex-wrap:wrap;gap:8px}.tag{background:#dbeafe;color:#1e40af;padding:4px 8px;border-radius:4px;font-size:12px;font-weight:500}.workflow-picker{max-width:800px;margin:0 auto;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif}.workflow-step{margin:25px 0;padding:20px;border:1px solid #e5e7eb;border-radius:8px;background-color:#fff}.workflow-step label{display:block;margin-bottom:8px;font-weight:600;color:#374151}.dataset-dropdown,.workflow-step input,.workflow-step select{width:100%;padding:10px 12px;border:1px solid #d1d5db;border-radius:6px;font-size:14px;background-color:#fff}.dataset-dropdown:focus,.workflow-step input:focus,.workflow-step select:focus{outline:0;border-color:#3b82f6;box-shadow:0 0 0 3px rgba(59,130,246,.1)}.dataset-preview{margin-top:20px;padding:20px;border:1px solid #e5e7eb;border-radius:8px;background-color:#fff}.dataset-info{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:15px;margin-bottom:20px}.info-item{padding:10px;background-color:#f3f4f6;border-radius:4px}.data-preview table,.data-preview-table,.schema-table table{width:100%;border-collapse:collapse;margin-top:10px}.data-preview table,.data-preview-table,.query-results-table,.schema-table table{width:100%;border-collapse:collapse;margin-top:10px;background:#fff;border-radius:8px;overflow:hidden;box-shadow:0 1px 3px rgba(0,0,0,.1)}.data-preview td,.data-preview th,.data-preview-table td,.data-preview-table th,.query-results-table td,.query-results-table th,.schema-table td,.schema-table th{padding:12px 16px;text-align:left;border-bottom:1px solid #e5e7eb}.data-preview th,.data-preview-table th,.query-results-table th,.schema-table th{background-color:#f8fafc;font-weight:600;color:#374151;font-size:14px}.column-name{font-family:Monaco,Menlo,monospace;font-weight:500}.type-badge{padding:4px 8px;border-radius:4px;font-size:11px;font-weight:600;text-transform:uppercase}.type-string{background:#dbeafe;color:#1e40af}.type-long{background:#dcfce7;color:#166534}.type-double{background:#fef3c7;color:#92400e}.type-date{background:#e0e7ff;color:#3730a3}.type-datetime{background:#e0e7ff;color:#3730a3}.type-boolean{background:#fce7f3;color:#be185d}.table-container{max-height:400px;overflow:auto;border-radius:8px;border:1px solid #e5e7eb}.field-help{display:block;margin-top:4px;font-size:12px;color:#6b7280;font-style:italic}.workflow-step input.valid{border-color:#10b981;background-color:#f0fdf4}.workflow-step input.invalid{border-color:#ef4444;background-color:#fef2f2}.validation-results{margin-top:15px;padding:15px;border-radius:8px;background:#f9fafb;border:1px solid #e5e7eb}.validation-item{display:flex;align-items:center;margin-bottom:8px;padding:8px;border-radius:4px}.validation-item:last-child{margin-bottom:0}.validation-success{background:#f0fdf4;color:#166534}.validation-error{background:#fef2f2;color:#dc2626}.validation-warning{background:#fffbeb;color:#d97706}.validation-info{background:#eff6ff;color:#2563eb}.validation-icon{margin-right:8px}.progress-bar{width:100%;height:8px;background:#e5e7eb;border-radius:4px;overflow:hidden;margin-top:10px}.progress-fill{height:100%;background:#3b82f6;transition:width .3s ease;width:0%}.data-preview{margin-top:15px;padding:15px;border:1px solid #e5e7eb;border-radius:6px;background-color:#f9fafb}.config-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px}.workflow-actions{text-align:center;margin:30px 0}.btn{padding:12px 24px;border:none;border-radius:6px;font-size:14px;font-weight:600;cursor:pointer;transition:all .2s;margin:0 5px}.btn:disabled{opacity:.6;cursor:not-allowed}.btn-primary{background-color:#3b82f6;color:#fff}.btn-primary:hover:not(:disabled){background-color:#2563eb}.btn-secondary{background-color:#f3f4f6;color:#374151;border:1px solid #d1d5db}.btn-secondary:hover:not(:disabled){background-color:#e5e7eb}.loading-status{margin:20px 0;padding:20px;border:1px solid #e5e7eb;border-radius:8px;background-color:#f9fafb;text-align:center}.loading-spinner{width:40px;height:40px;border:4px solid #f3f4f6;border-top:4px solid #3b82f6;border-radius:50%;animation:spin 1s linear infinite;margin:0 auto 10px}@keyframes spin{0%{transform:rotate(0)}100%{transform:rotate(360deg)}}.loaded-datasets-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.loaded-datasets-grid{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:20px}.loaded-dataset-card{background:#fff;border:1px solid #e5e7eb;border-radius:8px;padding:20px;transition:box-shadow .2s}.loaded-dataset-card:hover{box-shadow:0 4px 6px rgba(0,0,0,.1)}.loaded-dataset-card h4{margin:0 0 8px 0;color:#1f2937;font-family:Monaco,Menlo,monospace}.dataset-source{color:#6b7280;font-size:14px;margin-bottom:12px}.dataset-stats{display:flex;gap:15px;margin-bottom:15px;font-size:12px;color:#9ca3af}.dataset-actions{display:flex;gap:8px}.btn-small{padding:6px 12px;font-size:12px}.query-interface{background:#fff;border:1px solid #e5e7eb;border-radius:8px;padding:20px;margin-top:20px}.query-input{margin-bottom:20px}.query-input label{display:block;margin-bottom:8px;font-weight:600;color:#374151}.query-input textarea{width:100%;min-height:120px;padding:12px;border:1px solid #d1d5db;border-radius:6px;font-family:Monaco,Menlo,monospace;font-size:14px;resize:vertical}.query-input textarea:focus{outline:0;border-color:#3b82f6;box-shadow:0 0 0 3px rgba(59,130,246,.1)}.query-actions{margin-top:10px;display:flex;gap:10px}.query-results{margin-top:20px}.query-results-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:15px;padding-bottom:10px;border-bottom:1px solid #e5e7eb}.query-results-header h4{margin:0;color:#1f2937}.query-stats{display:flex;gap:15px;font-size:14px;color:#6b7280}.metadata-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:20px}.metadata-item{background:#f9fafb;padding:15px;border-radius:6px;border:1px solid #e5e7eb}.metadata-item h6{margin:0 0 8px 0;color:#374151;font-size:14px;font-weight:600}.metadata-item p{margin:0;color:#6b7280;font-size:13px;line-height:1.4}.metadata-item code{background:#f3f4f6;padding:2px 4px;border-radius:3px;font-size:12px;color:#1f2937}.action-buttons{display:flex;gap:12px;justify-content:center;flex-wrap:wrap}.preview-note{font-size:12px;color:#6b7280;margin-left:10px}@media (max-width:768px){.workflow-picker-section{padding:20px}.config-grid{grid-template-columns:1fr}.loaded-datasets-grid{grid-template-columns:1fr}.action-buttons{flex-direction:column}.dataset-tabs{flex-wrap:wrap}.tab-button{flex:1;min-width:120px}}</style></body></html>