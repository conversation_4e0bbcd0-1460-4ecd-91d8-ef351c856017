import{s as It,v as Q,d as o,i as p,e as v,b as e,z as vt,A as at,k as _,B as At,h as c,r as lt,q as w,j as S,n as h,m as u,t as Lt,C as Ot,D as St,E as Vt,F as qt,x as Ft,y as Bt}from"../chunks/scheduler.D0cbHTIG.js";import{S as Nt,i as Pt}from"../chunks/index.YnsWT1Qn.js";import{e as jt,s as Qt,p as Rt,a as Mt,r as gt,C as Gt}from"../chunks/inferColumnTypes.CZxLq2wp.js";import{p as Kt}from"../chunks/profile.BW8tN6E9.js";import{p as Ut}from"../chunks/stores.lbTreEGG.js";import{w as Xt}from"../chunks/entry.5ejmxmoV.js";function Yt(t){let a,s,n=m.title+"";return{c(){a=u("h1"),s=Bt(n),this.h()},l(e){a=c(e,"H1",{class:!0});var t=S(a);s=Ft(t,n),t.forEach(o),this.h()},h(){e(a,"class","title")},m(e,t){p(e,a,t),v(a,s)},p:Q,d(e){e&&o(a)}}}function Jt(e){return{c(){this.h()},l(e){this.h()},h(){document.title="Evidence"},m:Q,p:Q,d:Q}}function Wt(t){let a,s,n,i,l;return document.title=a=m.title,{c(){s=h(),n=u("meta"),i=h(),l=u("meta"),this.h()},l(e){s=_(e),n=c(e,"META",{property:!0,content:!0}),i=_(e),l=c(e,"META",{name:!0,content:!0}),this.h()},h(){var t,a;e(n,"property","og:title"),e(n,"content",(null==(t=m.og)?void 0:t.title)??m.title),e(l,"name","twitter:title"),e(l,"content",(null==(a=m.og)?void 0:a.title)??m.title)},m(e,t){p(e,s,t),p(e,n,t),p(e,i,t),p(e,l,t)},p(e,t){0&t&&a!==(a=m.title)&&(document.title=a)},d(e){e&&(o(s),o(n),o(i),o(l))}}}function Zt(e){var t,a;let s,n,i=(m.description||(null==(t=m.og)?void 0:t.description))&&$t(),l=(null==(a=m.og)?void 0:a.image)&&te();return{c(){i&&i.c(),s=h(),l&&l.c(),n=lt()},l(e){i&&i.l(e),s=_(e),l&&l.l(e),n=lt()},m(e,t){i&&i.m(e,t),p(e,s,t),l&&l.m(e,t),p(e,n,t)},p(e,t){var a,s;(m.description||null!=(a=m.og)&&a.description)&&i.p(e,t),null!=(s=m.og)&&s.image&&l.p(e,t)},d(e){e&&(o(s),o(n)),i&&i.d(e),l&&l.d(e)}}}function $t(t){let a,s,n,i,l;return{c(){a=u("meta"),s=h(),n=u("meta"),i=h(),l=u("meta"),this.h()},l(e){a=c(e,"META",{name:!0,content:!0}),s=_(e),n=c(e,"META",{property:!0,content:!0}),i=_(e),l=c(e,"META",{name:!0,content:!0}),this.h()},h(){var t,s,o;e(a,"name","description"),e(a,"content",m.description??(null==(t=m.og)?void 0:t.description)),e(n,"property","og:description"),e(n,"content",(null==(s=m.og)?void 0:s.description)??m.description),e(l,"name","twitter:description"),e(l,"content",(null==(o=m.og)?void 0:o.description)??m.description)},m(e,t){p(e,a,t),p(e,s,t),p(e,n,t),p(e,i,t),p(e,l,t)},p:Q,d(e){e&&(o(a),o(s),o(n),o(i),o(l))}}}function te(t){let a,s,n;return{c(){a=u("meta"),s=h(),n=u("meta"),this.h()},l(e){a=c(e,"META",{property:!0,content:!0}),s=_(e),n=c(e,"META",{name:!0,content:!0}),this.h()},h(){var t,s;e(a,"property","og:image"),e(a,"content",Mt(null==(t=m.og)?void 0:t.image)),e(n,"name","twitter:image"),e(n,"content",Mt(null==(s=m.og)?void 0:s.image))},m(e,t){p(e,a,t),p(e,s,t),p(e,n,t)},p:Q,d(e){e&&(o(a),o(s),o(n))}}}function ee(t){let a,s,n,i,l,d,r,f,g,y,z,b,T,L,D,E,M,k,x,C,H,I,j,A,V,$,O,q,B,F,N,P,G,R,W,Y,Z,J,K,U='<a href="#domo-data-loader">Domo Data Loader</a>',X="Load datasets from Domo into DuckDB for analysis.",ee="Select Dataset:",te="Choose a dataset...",ae='<h4>Dataset Information</h4> <div id="preview-content" class="preview-content"><div id="dataset-info" class="dataset-info svelte-16omzez"></div> <h5>Schema</h5> <div id="schema-table" class="schema-table"></div> <div class="preview-actions svelte-16omzez"><button id="preview-btn" class="btn btn-secondary svelte-16omzez">Preview Data</button></div> <div id="data-preview" class="data-preview svelte-16omzez" style="display: none;"></div></div>',se="Loading Configuration",oe='<label for="table-name" class="svelte-16omzez">Table Name in DuckDB:</label> <input id="table-name" type="text" placeholder="Enter table name" class="svelte-16omzez"/>',ne="Refresh Mode:",ie="Replace existing data",le="Append to existing data",ce='<button id="load-dataset-btn" class="btn btn-primary svelte-16omzez">📊 Load Dataset into DuckDB</button>',de='<div class="loading-spinner svelte-16omzez"></div> <p class="svelte-16omzez">Loading...</p>',re='<a href="#loaded-datasets">Loaded Datasets</a>',me="<p>Once you load datasets from Domo, they will appear here and you can query them using SQL.</p>",ve=typeof m<"u"&&m.title&&!0!==m.hide_title&&Yt(),pe=(typeof m<"u"&&m.title?Wt:Jt)(t),ue="object"==typeof m&&Zt();return{c(){ve&&ve.c(),a=h(),pe.c(),s=u("meta"),n=u("meta"),ue&&ue.c(),i=lt(),l=h(),d=u("h1"),d.innerHTML=U,r=h(),f=u("p"),f.textContent=X,g=h(),y=u("div"),z=u("div"),b=u("div"),T=u("label"),T.textContent=ee,L=h(),D=u("select"),E=u("option"),E.textContent=te,M=h(),k=u("div"),k.innerHTML=ae,x=h(),C=u("div"),H=u("h4"),H.textContent=se,I=h(),j=u("div"),A=u("div"),A.innerHTML=oe,V=h(),$=u("div"),O=u("label"),O.textContent=ne,q=h(),B=u("select"),F=u("option"),F.textContent=ie,N=u("option"),N.textContent=le,P=h(),G=u("div"),G.innerHTML=ce,R=h(),W=u("div"),W.innerHTML=de,Y=h(),Z=u("h2"),Z.innerHTML=re,J=h(),K=u("div"),K.innerHTML=me,this.h()},l(e){ve&&ve.l(e),a=_(e);const t=At("svelte-2igo1p",document.head);pe.l(t),s=c(t,"META",{name:!0,content:!0}),n=c(t,"META",{name:!0,content:!0}),ue&&ue.l(t),i=lt(),t.forEach(o),l=_(e),d=c(e,"H1",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-94lco6"!==w(d)&&(d.innerHTML=U),r=_(e),f=c(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-4aovu"!==w(f)&&(f.textContent=X),g=_(e),y=c(e,"DIV",{class:!0});var m=S(y);z=c(m,"DIV",{id:!0,class:!0});var v=S(z);b=c(v,"DIV",{class:!0});var p=S(b);T=c(p,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),"svelte-1fci9ty"!==w(T)&&(T.textContent=ee),L=_(p),D=c(p,"SELECT",{id:!0,class:!0});var u=S(D);E=c(u,"OPTION",{"data-svelte-h":!0}),"svelte-59d9xk"!==w(E)&&(E.textContent=te),u.forEach(o),p.forEach(o),M=_(v),k=c(v,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),"svelte-1bdnaam"!==w(k)&&(k.innerHTML=ae),x=_(v),C=c(v,"DIV",{id:!0,class:!0,style:!0});var h=S(C);H=c(h,"H4",{"data-svelte-h":!0}),"svelte-1foy07w"!==w(H)&&(H.textContent=se),I=_(h),j=c(h,"DIV",{class:!0});var Q=S(j);A=c(Q,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1y7um71"!==w(A)&&(A.innerHTML=oe),V=_(Q),$=c(Q,"DIV",{class:!0});var he=S($);O=c(he,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),"svelte-p1qydn"!==w(O)&&(O.textContent=ne),q=_(he),B=c(he,"SELECT",{id:!0,class:!0});var fe=S(B);F=c(fe,"OPTION",{"data-svelte-h":!0}),"svelte-qvzdub"!==w(F)&&(F.textContent=ie),N=c(fe,"OPTION",{"data-svelte-h":!0}),"svelte-idsvi6"!==w(N)&&(N.textContent=le),fe.forEach(o),he.forEach(o),Q.forEach(o),h.forEach(o),P=_(v),G=c(v,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),"svelte-1jj2g91"!==w(G)&&(G.innerHTML=ce),R=_(v),W=c(v,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),"svelte-9gnlc9"!==w(W)&&(W.innerHTML=de),v.forEach(o),m.forEach(o),Y=_(e),Z=c(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-1f0ly50"!==w(Z)&&(Z.innerHTML=re),J=_(e),K=c(e,"DIV",{id:!0,"data-svelte-h":!0}),"svelte-187xr4d"!==w(K)&&(K.innerHTML=me),this.h()},h(){e(s,"name","twitter:card"),e(s,"content","summary_large_image"),e(n,"name","twitter:site"),e(n,"content","@evidence_dev"),e(d,"class","markdown"),e(d,"id","domo-data-loader"),e(f,"class","markdown"),e(T,"for","dataset-selector"),e(T,"class","svelte-16omzez"),E.__value="",vt(E,E.__value),e(D,"id","dataset-selector"),e(D,"class","dataset-dropdown svelte-16omzez"),e(b,"class","workflow-step svelte-16omzez"),e(k,"id","dataset-preview"),e(k,"class","dataset-preview svelte-16omzez"),at(k,"display","none"),e(A,"class","config-item"),e(O,"for","refresh-mode"),e(O,"class","svelte-16omzez"),F.__value="replace",vt(F,F.__value),N.__value="append",vt(N,N.__value),e(B,"id","refresh-mode"),e(B,"class","svelte-16omzez"),e($,"class","config-item"),e(j,"class","config-grid svelte-16omzez"),e(C,"id","loading-config"),e(C,"class","workflow-step svelte-16omzez"),at(C,"display","none"),e(G,"id","workflow-actions"),e(G,"class","workflow-actions svelte-16omzez"),at(G,"display","none"),e(W,"id","loading-status"),e(W,"class","loading-status svelte-16omzez"),at(W,"display","none"),e(z,"id","domo-workflow-picker"),e(z,"class","workflow-picker svelte-16omzez"),e(y,"class","workflow-picker-section svelte-16omzez"),e(Z,"class","markdown"),e(Z,"id","loaded-datasets"),e(K,"id","loaded-datasets-section")},m(e,t){ve&&ve.m(e,t),p(e,a,t),pe.m(document.head,null),v(document.head,s),v(document.head,n),ue&&ue.m(document.head,null),v(document.head,i),p(e,l,t),p(e,d,t),p(e,r,t),p(e,f,t),p(e,g,t),p(e,y,t),v(y,z),v(z,b),v(b,T),v(b,L),v(b,D),v(D,E),v(z,M),v(z,k),v(z,x),v(z,C),v(C,H),v(C,I),v(C,j),v(j,A),v(j,V),v(j,$),v($,O),v($,q),v($,B),v(B,F),v(B,N),v(z,P),v(z,G),v(z,R),v(z,W),p(e,Y,t),p(e,Z,t),p(e,J,t),p(e,K,t)},p(e,[t]){typeof m<"u"&&m.title&&!0!==m.hide_title&&ve.p(e,t),pe.p(e,t),"object"==typeof m&&ue.p(e,t)},i:Q,o:Q,d(e){e&&(o(a),o(l),o(d),o(r),o(f),o(g),o(y),o(Y),o(Z),o(J),o(K)),ve&&ve.d(e),pe.d(e),o(s),o(n),ue&&ue.d(e),o(i)}}}const m={title:"Domo Data Loader"};function ae(e,t,a){let s,o;Lt(e,Ut,(e=>a(2,s=e))),Lt(e,gt,(e=>a(8,o=e)));let{data:n}=t,{data:i={},customFormattingSettings:l,__db:c,inputs:d}=n;Ot(gt,o="6666cd76f96956469e7be39d750cc7d9",o);let r=jt(Xt(d));return St(r.subscribe((e=>d=e))),Vt(Gt,{getCustomFormats:()=>l.customFormats||[]}),Qt(((e,t)=>Kt(c.query,e,{query_name:t}))),s.params,qt((()=>!0)),e.$$set=e=>{"data"in e&&a(0,n=e.data)},e.$$.update=()=>{1&e.$$.dirty&&a(1,({data:i={},customFormattingSettings:l,__db:c}=n),i),2&e.$$.dirty&&Rt.set(Object.keys(i).length>0),4&e.$$.dirty&&s.params},[n,i,s]}class ce extends Nt{constructor(e){super(),Pt(this,e,ae,ee,It,{data:0})}}export{ce as component};