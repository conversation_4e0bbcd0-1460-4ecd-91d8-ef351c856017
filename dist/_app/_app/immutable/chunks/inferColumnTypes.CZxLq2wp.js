var ye,Co,xl=Object.defineProperty,No=t=>{throw TypeError(t)},Pl=(t,e,i)=>e in t?xl(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i,It=(t,e,i)=>Pl(t,"symbol"!=typeof e?e+"":e,i),Bo=(t,e,i)=>e.has(t)||No("Cannot "+i),u=(t,e,i)=>(Bo(t,e,"read from private field"),i?i.call(t):e.get(t)),j=(t,e,i)=>e.has(t)?No("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,i),nt=(t,e,i,r)=>(Bo(t,e,"write to private field"),r?r.call(t,i):e.set(t,i),i),Ro=(t,e,i,r)=>({set _(r){nt(t,e,r,i)},get _(){return u(t,e,r)}});import{d as kl}from"./index.rV6zwFgL.js";import{w as Jr,b as $l,r as jl}from"./entry.5ejmxmoV.js";import{T as ws,an as pa,E as ya,a5 as ma}from"./scheduler.D0cbHTIG.js";import{_ as Hi}from"./preload-helper.D7HrI6pR.js";function Y(t,e,i,r){return new(i||(i=Promise))((function(n,s){function o(t){try{l(r.next(t))}catch(t){s(t)}}function a(t){try{l(r.throw(t))}catch(t){s(t)}}function l(t){t.done?n(t.value):function(t){return t instanceof i?t:new i((function(e){e(t)}))}(t.value).then(o,a)}l((r=r.apply(t,e||[])).next())}))}function Mo(t){var e="function"==typeof Symbol&&Symbol.iterator,i=e&&t[e],r=0;if(i)return i.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function q(t){return this instanceof q?(this.v=t,this):new q(t)}function Ae(t,e,i){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,n=i.apply(t,e||[]),s=[];return r=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",(function(t){return function(e){return Promise.resolve(e).then(t,c)}})),r[Symbol.asyncIterator]=function(){return this},r;function o(t,e){n[t]&&(r[t]=function(e){return new Promise((function(i,r){s.push([t,e,i,r])>1||a(t,e)}))},e&&(r[t]=e(r[t])))}function a(t,e){try{!function(t){t.value instanceof q?Promise.resolve(t.value.v).then(l,c):u(s[0][2],t)}(n[t](e))}catch(t){u(s[0][3],t)}}function l(t){a("next",t)}function c(t){a("throw",t)}function u(t,e){t(e),s.shift(),s.length&&a(s[0][0],s[0][1])}}function Xi(t){var e,i;return e={},r("next"),r("throw",(function(t){throw t})),r("return"),e[Symbol.iterator]=function(){return this},e;function r(r,n){e[r]=t[r]?function(e){return(i=!i)?{value:q(t[r](e)),done:!1}:n?n(e):e}:n}}function In(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,i=t[Symbol.asyncIterator];return i?i.call(t):(t="function"==typeof Mo?Mo(t):t[Symbol.iterator](),e={},r("next"),r("throw"),r("return"),e[Symbol.asyncIterator]=function(){return this},e);function r(i){e[i]=t[i]&&function(e){return new Promise((function(r,n){!function(t,e,i,r){Promise.resolve(r).then((function(e){t({value:e,done:i})}),e)}(r,n,(e=t[i](e)).done,e.value)}))}}}function Vl(t){switch(t.typeId){case ye.Date:return"date";case ye.Float:case ye.Int:return"number";case ye.Bool:return"boolean";case ye.Dictionary:default:return"string"}}function zl(t){if(null==t)return[];const e=t.toArray();Object.defineProperty(e,"_evidenceColumnTypes",{enumerable:!1,value:t.schema.fields.map((t=>({name:t.name,evidenceType:Vl(t.type),typeFidelity:"precise"})))});const i=t.schema.fields.filter((t=>t.type.typeId===ye.Date)),r=t.schema.fields.filter((t=>t.type.typeId===ye.List));for(const t of e){for(const e of i)t[e.name]=new Date(t[e.name]);for(const e of r)t[e.name]=ga(t[e.name])}return e}function ga(t){var e,i,r,n;if(null==t)return[];const s=t.toArray(),o=(null==(i=null==(e=t.type)?void 0:e.children)?void 0:i.filter((t=>t.type.typeId===ye.Date)))??[],a=(null==(n=null==(r=t.type)?void 0:r.children)?void 0:n.filter((t=>t.type.typeId===ye.List)))??[];for(const t of s){for(const e of o)t[e.name]=new Date(t[e.name]);for(const e of a)t[e.name]=ga(t[e.name])}return s}function ba(){let t,e,i=new Promise(((i,r)=>{t=i,e=r}));return{resolve:t,reject:e,promise:i}}function _a(t){return Promise.race([t,new Promise(((t,e)=>setTimeout((()=>e(new Error("Timeout while initializing database"))),5e3)))])}!function(t){t[t.NONE=0]="NONE",t[t.Null=1]="Null",t[t.Int=2]="Int",t[t.Float=3]="Float",t[t.Binary=4]="Binary",t[t.Utf8=5]="Utf8",t[t.Bool=6]="Bool",t[t.Decimal=7]="Decimal",t[t.Date=8]="Date",t[t.Time=9]="Time",t[t.Timestamp=10]="Timestamp",t[t.Interval=11]="Interval",t[t.List=12]="List",t[t.Struct=13]="Struct",t[t.Union=14]="Union",t[t.FixedSizeBinary=15]="FixedSizeBinary",t[t.FixedSizeList=16]="FixedSizeList",t[t.Map=17]="Map",t[t.Duration=18]="Duration",t[t.LargeBinary=19]="LargeBinary",t[t.LargeUtf8=20]="LargeUtf8",t[t.Dictionary=-1]="Dictionary",t[t.Int8=-2]="Int8",t[t.Int16=-3]="Int16",t[t.Int32=-4]="Int32",t[t.Int64=-5]="Int64",t[t.Uint8=-6]="Uint8",t[t.Uint16=-7]="Uint16",t[t.Uint32=-8]="Uint32",t[t.Uint64=-9]="Uint64",t[t.Float16=-10]="Float16",t[t.Float32=-11]="Float32",t[t.Float64=-12]="Float64",t[t.DateDay=-13]="DateDay",t[t.DateMillisecond=-14]="DateMillisecond",t[t.TimestampSecond=-15]="TimestampSecond",t[t.TimestampMillisecond=-16]="TimestampMillisecond",t[t.TimestampMicrosecond=-17]="TimestampMicrosecond",t[t.TimestampNanosecond=-18]="TimestampNanosecond",t[t.TimeSecond=-19]="TimeSecond",t[t.TimeMillisecond=-20]="TimeMillisecond",t[t.TimeMicrosecond=-21]="TimeMicrosecond",t[t.TimeNanosecond=-22]="TimeNanosecond",t[t.DenseUnion=-23]="DenseUnion",t[t.SparseUnion=-24]="SparseUnion",t[t.IntervalDayTime=-25]="IntervalDayTime",t[t.IntervalYearMonth=-26]="IntervalYearMonth",t[t.DurationSecond=-27]="DurationSecond",t[t.DurationMillisecond=-28]="DurationMillisecond",t[t.DurationMicrosecond=-29]="DurationMicrosecond",t[t.DurationNanosecond=-30]="DurationNanosecond"}(ye||(ye={})),function(t){t[t.OFFSET=0]="OFFSET",t[t.DATA=1]="DATA",t[t.VALIDITY=2]="VALIDITY",t[t.TYPE=3]="TYPE"}(Co||(Co={}));const Wl=new TextDecoder("utf-8"),Is=t=>Wl.decode(t),Yl=new TextEncoder,$s=t=>Yl.encode(t),Gl=t=>"number"==typeof t,wa=t=>"boolean"==typeof t,Rt=t=>"function"==typeof t,Jt=t=>null!=t&&Object(t)===t,Sn=t=>Jt(t)&&Rt(t.then),$i=t=>Jt(t)&&Rt(t[Symbol.iterator]),ei=t=>Jt(t)&&Rt(t[Symbol.asyncIterator]),vs=t=>Jt(t)&&Jt(t.schema),Ia=t=>Jt(t)&&"done"in t&&"value"in t,va=t=>Jt(t)&&Rt(t.stat)&&Gl(t.fd),Sa=t=>Jt(t)&&js(t.body),Kr=t=>"_getDOMStream"in t&&"_getNodeStream"in t,ql=t=>Jt(t)&&Rt(t.abort)&&Rt(t.getWriter)&&!Kr(t),js=t=>Jt(t)&&Rt(t.cancel)&&Rt(t.getReader)&&!Kr(t),Hl=t=>Jt(t)&&Rt(t.end)&&Rt(t.write)&&wa(t.writable)&&!Kr(t),Ea=t=>Jt(t)&&Rt(t.read)&&Rt(t.pipe)&&wa(t.readable)&&!Kr(t),Ql=t=>Jt(t)&&Rt(t.clear)&&Rt(t.bytes)&&Rt(t.position)&&Rt(t.setPosition)&&Rt(t.capacity)&&Rt(t.getBufferIdentifier)&&Rt(t.createLong),Vs=typeof SharedArrayBuffer<"u"?SharedArrayBuffer:ArrayBuffer;function Jl(t){const e=t[0]?[t[0]]:[];let i,r,n,s;for(let o,a,l=0,c=0,u=t.length;++l<u;)o=e[c],a=t[l],!o||!a||o.buffer!==a.buffer||a.byteOffset<o.byteOffset?a&&(e[++c]=a):(({byteOffset:i,byteLength:n}=o),({byteOffset:r,byteLength:s}=a),i+n<r||r+s<i?a&&(e[++c]=a):e[c]=new Uint8Array(o.buffer,i,r-i+s));return e}function Lo(t,e,i=0,r=e.byteLength){const n=t.byteLength,s=new Uint8Array(t.buffer,t.byteOffset,n),o=new Uint8Array(e.buffer,e.byteOffset,Math.min(r,n));return s.set(o,i),t}function Oe(t,e){const i=Jl(t),r=i.reduce(((t,e)=>t+e.byteLength),0);let n,s,o,a=0,l=-1;const c=Math.min(e||Number.POSITIVE_INFINITY,r);for(const t=i.length;++l<t;){if(n=i[l],s=n.subarray(0,Math.min(n.length,c-a)),c<=a+s.length){s.length<n.length?i[l]=n.subarray(s.length):s.length===n.length&&l++,o?Lo(o,s,a):o=s;break}Lo(o||(o=new Uint8Array(c)),s,a),a+=s.length}return[o||new Uint8Array(0),i.slice(l),r-(o?o.byteLength:0)]}function ft(t,e){let i=Ia(e)?e.value:e;return i instanceof t?t===Uint8Array?new t(i.buffer,i.byteOffset,i.byteLength):i:i?("string"==typeof i&&(i=$s(i)),i instanceof ArrayBuffer||i instanceof Vs?new t(i):Ql(i)?ft(t,i.bytes()):ArrayBuffer.isView(i)?i.byteLength<=0?new t(0):new t(i.buffer,i.byteOffset,i.byteLength/t.BYTES_PER_ELEMENT):t.from(i)):new t(0)}const li=t=>ft(Int32Array,t),Uo=t=>ft(BigInt64Array,t),rt=t=>ft(Uint8Array,t),Ss=t=>(t.next(),t);function*Kl(t,e){const i=function*(t){yield t},r="string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof Vs?i(e):$i(e)?e:i(e);return yield*Ss(function*(e){let i=null;do{i=e.next(yield ft(t,i))}while(!i.done)}(r[Symbol.iterator]())),new t}const Zl=t=>Kl(Uint8Array,t);function Ta(t,e){return Ae(this,arguments,(function*(){if(Sn(e))return yield q(yield q(yield*Xi(In(Ta(t,yield q(e))))));const i=function(t){return Ae(this,arguments,(function*(){yield yield q(yield q(t))}))},r="string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof Vs?i(e):$i(e)?function(t){return Ae(this,arguments,(function*(){yield q(yield*Xi(In(Ss(function*(t){let e=null;do{e=t.next(yield null==e?void 0:e.value)}while(!e.done)}(t[Symbol.iterator]())))))}))}(e):ei(e)?e:i(e);return yield q(yield*Xi(In(Ss(function(e){return Ae(this,arguments,(function*(){let i=null;do{i=yield q(e.next(yield yield q(ft(t,i))))}while(!i.done)}))}(r[Symbol.asyncIterator]()))))),yield q(new t)}))}const Xl=t=>Ta(Uint8Array,t);function Aa(t,e,i){if(0!==t)for(let r=-1,n=(i=i.slice(0,e)).length;++r<n;)i[r]+=t;return i.subarray(0,e)}function tu(t,e){let i=0;const r=t.length;if(r!==e.length)return!1;if(r>0)do{if(t[i]!==e[i])return!1}while(++i<r);return!0}const se={fromIterable:t=>Qi(eu(t)),fromAsyncIterable:t=>Qi(nu(t)),fromDOMStream:t=>Qi(iu(t)),fromNodeStream:t=>Qi(su(t)),toDOMStream(t,e){throw new Error('"toDOMStream" not available in this environment')},toNodeStream(t,e){throw new Error('"toNodeStream" not available in this environment')}},Qi=t=>(t.next(),t);function*eu(t){let e,i,r,n,s=!1,o=[],a=0;({cmd:r,size:n}=(yield null)||{cmd:"read",size:0});const l=Zl(t)[Symbol.iterator]();try{do{if(({done:e,value:i}=Number.isNaN(n-a)?l.next():l.next(n-a)),!e&&i.byteLength>0&&(o.push(i),a+=i.byteLength),e||n<=a)do{({cmd:r,size:n}=yield"peek"===r?Oe(o,n)[0]:([i,o,a]=Oe(o,n),i))}while(n<a)}while(!e)}catch(t){(s=!0)&&"function"==typeof l.throw&&l.throw(t)}finally{!1===s&&"function"==typeof l.return&&l.return(null)}return null}function nu(t){return Ae(this,arguments,(function*(){let e,i,r,n,s=!1,o=[],a=0;({cmd:r,size:n}=(yield yield q(null))||{cmd:"read",size:0});const l=Xl(t)[Symbol.asyncIterator]();try{do{if(({done:e,value:i}=Number.isNaN(n-a)?yield q(l.next()):yield q(l.next(n-a))),!e&&i.byteLength>0&&(o.push(i),a+=i.byteLength),e||n<=a)do{({cmd:r,size:n}=yield yield q("peek"===r?Oe(o,n)[0]:([i,o,a]=Oe(o,n),i)))}while(n<a)}while(!e)}catch(t){(s=!0)&&"function"==typeof l.throw&&(yield q(l.throw(t)))}finally{!1===s&&"function"==typeof l.return&&(yield q(l.return(new Uint8Array(0))))}return yield q(null)}))}function iu(t){return Ae(this,arguments,(function*(){let e,i,r,n=!1,s=!1,o=[],a=0;({cmd:i,size:r}=(yield yield q(null))||{cmd:"read",size:0});const l=new ru(t);try{do{if(({done:n,value:e}=Number.isNaN(r-a)?yield q(l.read()):yield q(l.read(r-a))),!n&&e.byteLength>0&&(o.push(rt(e)),a+=e.byteLength),n||r<=a)do{({cmd:i,size:r}=yield yield q("peek"===i?Oe(o,r)[0]:([e,o,a]=Oe(o,r),e)))}while(r<a)}while(!n)}catch(t){(s=!0)&&(yield q(l.cancel(t)))}finally{!1===s?yield q(l.cancel()):t.locked&&l.releaseLock()}return yield q(null)}))}class ru{constructor(t){this.source=t,this.reader=null,this.reader=this.source.getReader(),this.reader.closed.catch((()=>{}))}get closed(){return this.reader?this.reader.closed.catch((()=>{})):Promise.resolve()}releaseLock(){this.reader&&this.reader.releaseLock(),this.reader=null}cancel(t){return Y(this,void 0,void 0,(function*(){const{reader:e,source:i}=this;e&&(yield e.cancel(t).catch((()=>{}))),i&&i.locked&&this.releaseLock()}))}read(t){return Y(this,void 0,void 0,(function*(){if(0===t)return{done:null==this.reader,value:new Uint8Array(0)};const e=yield this.reader.read();return!e.done&&(e.value=rt(e)),e}))}}const ss=(t,e)=>{const i=t=>r([e,t]);let r;return[e,i,new Promise((n=>(r=n)&&t.once(e,i)))]};function su(t){return Ae(this,arguments,(function*(){const e=[];let i,r,n,s="error",o=!1,a=null,l=0,c=[];if(({cmd:i,size:r}=(yield yield q(null))||{cmd:"read",size:0}),t.isTTY)return yield yield q(new Uint8Array(0)),yield q(null);try{e[0]=ss(t,"end"),e[1]=ss(t,"error");do{if(e[2]=ss(t,"readable"),[s,a]=yield q(Promise.race(e.map((t=>t[2])))),"error"===s)break;if((o="end"===s)||(Number.isFinite(r-l)?(n=rt(t.read(r-l)),n.byteLength<r-l&&(n=rt(t.read()))):n=rt(t.read()),n.byteLength>0&&(c.push(n),l+=n.byteLength)),o||r<=l)do{({cmd:i,size:r}=yield yield q("peek"===i?Oe(c,r)[0]:([n,c,l]=Oe(c,r),n)))}while(r<l)}while(!o)}finally{yield q((u=e,h="error"===s?a:null,n=c=null,new Promise(((e,i)=>{for(const[e,i]of u)t.off(e,i);try{const e=t.destroy;e&&e.call(t,h),h=void 0}catch(t){h=t||h}finally{null!=h?i(h):e()}}))))}var u,h;return yield q(null)}))}var Dt,Pt,xt,le,x,De;!function(t){t[t.V1=0]="V1",t[t.V2=1]="V2",t[t.V3=2]="V3",t[t.V4=3]="V4",t[t.V5=4]="V5"}(Dt||(Dt={})),function(t){t[t.Sparse=0]="Sparse",t[t.Dense=1]="Dense"}(Pt||(Pt={})),function(t){t[t.HALF=0]="HALF",t[t.SINGLE=1]="SINGLE",t[t.DOUBLE=2]="DOUBLE"}(xt||(xt={})),function(t){t[t.DAY=0]="DAY",t[t.MILLISECOND=1]="MILLISECOND"}(le||(le={})),function(t){t[t.SECOND=0]="SECOND",t[t.MILLISECOND=1]="MILLISECOND",t[t.MICROSECOND=2]="MICROSECOND",t[t.NANOSECOND=3]="NANOSECOND"}(x||(x={})),function(t){t[t.YEAR_MONTH=0]="YEAR_MONTH",t[t.DAY_TIME=1]="DAY_TIME",t[t.MONTH_DAY_NANO=2]="MONTH_DAY_NANO"}(De||(De={}));const os=2,Se=4,$e=4,ut=4,Je=new Int32Array(2),xo=new Float32Array(Je.buffer),Po=new Float64Array(Je.buffer),Ji=1===new Uint16Array(new Uint8Array([1,0]).buffer)[0];var Es;!function(t){t[t.UTF8_BYTES=1]="UTF8_BYTES",t[t.UTF16_STRING=2]="UTF16_STRING"}(Es||(Es={}));let Jn=class t{constructor(t){this.bytes_=t,this.position_=0,this.text_decoder_=new TextDecoder}static allocate(e){return new t(new Uint8Array(e))}clear(){this.position_=0}bytes(){return this.bytes_}position(){return this.position_}setPosition(t){this.position_=t}capacity(){return this.bytes_.length}readInt8(t){return this.readUint8(t)<<24>>24}readUint8(t){return this.bytes_[t]}readInt16(t){return this.readUint16(t)<<16>>16}readUint16(t){return this.bytes_[t]|this.bytes_[t+1]<<8}readInt32(t){return this.bytes_[t]|this.bytes_[t+1]<<8|this.bytes_[t+2]<<16|this.bytes_[t+3]<<24}readUint32(t){return this.readInt32(t)>>>0}readInt64(t){return BigInt.asIntN(64,BigInt(this.readUint32(t))+(BigInt(this.readUint32(t+4))<<BigInt(32)))}readUint64(t){return BigInt.asUintN(64,BigInt(this.readUint32(t))+(BigInt(this.readUint32(t+4))<<BigInt(32)))}readFloat32(t){return Je[0]=this.readInt32(t),xo[0]}readFloat64(t){return Je[Ji?0:1]=this.readInt32(t),Je[Ji?1:0]=this.readInt32(t+4),Po[0]}writeInt8(t,e){this.bytes_[t]=e}writeUint8(t,e){this.bytes_[t]=e}writeInt16(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8}writeUint16(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8}writeInt32(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24}writeUint32(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24}writeInt64(t,e){this.writeInt32(t,Number(BigInt.asIntN(32,e))),this.writeInt32(t+4,Number(BigInt.asIntN(32,e>>BigInt(32))))}writeUint64(t,e){this.writeUint32(t,Number(BigInt.asUintN(32,e))),this.writeUint32(t+4,Number(BigInt.asUintN(32,e>>BigInt(32))))}writeFloat32(t,e){xo[0]=e,this.writeInt32(t,Je[0])}writeFloat64(t,e){Po[0]=e,this.writeInt32(t,Je[Ji?0:1]),this.writeInt32(t+4,Je[Ji?1:0])}getBufferIdentifier(){if(this.bytes_.length<this.position_+4+4)throw new Error("FlatBuffers: ByteBuffer is too short to contain an identifier.");let t="";for(let e=0;e<4;e++)t+=String.fromCharCode(this.readInt8(this.position_+4+e));return t}__offset(t,e){const i=t-this.readInt32(t);return e<this.readInt16(i)?this.readInt16(i+e):0}__union(t,e){return t.bb_pos=e+this.readInt32(e),t.bb=this,t}__string(t,e){t+=this.readInt32(t);const i=this.readInt32(t);t+=4;const r=this.bytes_.subarray(t,t+i);return e===Es.UTF8_BYTES?r:this.text_decoder_.decode(r)}__union_with_string(t,e){return"string"==typeof t?this.__string(e):this.__union(t,e)}__indirect(t){return t+this.readInt32(t)}__vector(t){return t+this.readInt32(t)+4}__vector_len(t){return this.readInt32(t+this.readInt32(t))}__has_identifier(t){if(4!=t.length)throw new Error("FlatBuffers: file identifier must be length 4");for(let e=0;e<4;e++)if(t.charCodeAt(e)!=this.readInt8(this.position()+4+e))return!1;return!0}createScalarList(t,e){const i=[];for(let r=0;r<e;++r){const e=t(r);null!==e&&i.push(e)}return i}createObjList(t,e){const i=[];for(let r=0;r<e;++r){const e=t(r);null!==e&&i.push(e.unpack())}return i}},Oa=class t{constructor(t){let e;this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null,this.text_encoder=new TextEncoder,e=t||1024,this.bb=Jn.allocate(e),this.space=e}clear(){this.bb.clear(),this.space=this.bb.capacity(),this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null}forceDefaults(t){this.force_defaults=t}dataBuffer(){return this.bb}asUint8Array(){return this.bb.bytes().subarray(this.bb.position(),this.bb.position()+this.offset())}prep(e,i){e>this.minalign&&(this.minalign=e);const r=1+~(this.bb.capacity()-this.space+i)&e-1;for(;this.space<r+e+i;){const e=this.bb.capacity();this.bb=t.growByteBuffer(this.bb),this.space+=this.bb.capacity()-e}this.pad(r)}pad(t){for(let e=0;e<t;e++)this.bb.writeInt8(--this.space,0)}writeInt8(t){this.bb.writeInt8(this.space-=1,t)}writeInt16(t){this.bb.writeInt16(this.space-=2,t)}writeInt32(t){this.bb.writeInt32(this.space-=4,t)}writeInt64(t){this.bb.writeInt64(this.space-=8,t)}writeFloat32(t){this.bb.writeFloat32(this.space-=4,t)}writeFloat64(t){this.bb.writeFloat64(this.space-=8,t)}addInt8(t){this.prep(1,0),this.writeInt8(t)}addInt16(t){this.prep(2,0),this.writeInt16(t)}addInt32(t){this.prep(4,0),this.writeInt32(t)}addInt64(t){this.prep(8,0),this.writeInt64(t)}addFloat32(t){this.prep(4,0),this.writeFloat32(t)}addFloat64(t){this.prep(8,0),this.writeFloat64(t)}addFieldInt8(t,e,i){(this.force_defaults||e!=i)&&(this.addInt8(e),this.slot(t))}addFieldInt16(t,e,i){(this.force_defaults||e!=i)&&(this.addInt16(e),this.slot(t))}addFieldInt32(t,e,i){(this.force_defaults||e!=i)&&(this.addInt32(e),this.slot(t))}addFieldInt64(t,e,i){(this.force_defaults||e!==i)&&(this.addInt64(e),this.slot(t))}addFieldFloat32(t,e,i){(this.force_defaults||e!=i)&&(this.addFloat32(e),this.slot(t))}addFieldFloat64(t,e,i){(this.force_defaults||e!=i)&&(this.addFloat64(e),this.slot(t))}addFieldOffset(t,e,i){(this.force_defaults||e!=i)&&(this.addOffset(e),this.slot(t))}addFieldStruct(t,e,i){e!=i&&(this.nested(e),this.slot(t))}nested(t){if(t!=this.offset())throw new TypeError("FlatBuffers: struct must be serialized inline.")}notNested(){if(this.isNested)throw new TypeError("FlatBuffers: object serialization must not be nested.")}slot(t){null!==this.vtable&&(this.vtable[t]=this.offset())}offset(){return this.bb.capacity()-this.space}static growByteBuffer(t){const e=t.capacity();if(3221225472&e)throw new Error("FlatBuffers: cannot grow buffer beyond 2 gigabytes.");const i=e<<1,r=Jn.allocate(i);return r.setPosition(i-e),r.bytes().set(t.bytes(),i-e),r}addOffset(t){this.prep(4,0),this.writeInt32(this.offset()-t+4)}startObject(t){this.notNested(),null==this.vtable&&(this.vtable=[]),this.vtable_in_use=t;for(let e=0;e<t;e++)this.vtable[e]=0;this.isNested=!0,this.object_start=this.offset()}endObject(){if(null==this.vtable||!this.isNested)throw new Error("FlatBuffers: endObject called without startObject");this.addInt32(0);const t=this.offset();let e=this.vtable_in_use-1;for(;e>=0&&0==this.vtable[e];e--);const i=e+1;for(;e>=0;e--)this.addInt16(0!=this.vtable[e]?t-this.vtable[e]:0);this.addInt16(t-this.object_start);const r=2*(i+2);this.addInt16(r);let n=0;const s=this.space;t:for(e=0;e<this.vtables.length;e++){const t=this.bb.capacity()-this.vtables[e];if(r==this.bb.readInt16(t)){for(let e=2;e<r;e+=2)if(this.bb.readInt16(s+e)!=this.bb.readInt16(t+e))continue t;n=this.vtables[e];break}}return n?(this.space=this.bb.capacity()-t,this.bb.writeInt32(this.space,n-t)):(this.vtables.push(this.offset()),this.bb.writeInt32(this.bb.capacity()-t,this.offset()-t)),this.isNested=!1,t}finish(t,e,i){const r=i?4:0;if(e){const t=e;if(this.prep(this.minalign,8+r),4!=t.length)throw new TypeError("FlatBuffers: file identifier must be length 4");for(let e=3;e>=0;e--)this.writeInt8(t.charCodeAt(e))}this.prep(this.minalign,4+r),this.addOffset(t),r&&this.addInt32(this.bb.capacity()-this.space),this.bb.setPosition(this.space)}finishSizePrefixed(t,e){this.finish(t,e,!0)}requiredField(t,e){const i=this.bb.capacity()-t,r=i-this.bb.readInt32(i);if(!(e<this.bb.readInt16(r)&&0!=this.bb.readInt16(r+e)))throw new TypeError("FlatBuffers: field "+e+" must be set")}startVector(t,e,i){this.notNested(),this.vector_num_elems=e,this.prep(4,t*e),this.prep(i,t*e)}endVector(){return this.writeInt32(this.vector_num_elems),this.offset()}createSharedString(t){if(!t)return 0;if(this.string_maps||(this.string_maps=new Map),this.string_maps.has(t))return this.string_maps.get(t);const e=this.createString(t);return this.string_maps.set(t,e),e}createString(t){if(null==t)return 0;let e;return e=t instanceof Uint8Array?t:this.text_encoder.encode(t),this.addInt8(0),this.startVector(1,e.length,1),this.bb.setPosition(this.space-=e.length),this.bb.bytes().set(e,this.space),this.endVector()}createByteVector(t){return null==t?0:(this.startVector(1,t.length,1),this.bb.setPosition(this.space-=t.length),this.bb.bytes().set(t,this.space),this.endVector())}createObjectOffset(t){return null===t?0:"string"==typeof t?this.createString(t):t.pack(this)}createObjectOffsetList(t){const e=[];for(let i=0;i<t.length;++i){const r=t[i];if(null===r)throw new TypeError("FlatBuffers: Argument for createObjectOffsetList cannot contain null.");e.push(this.createObjectOffset(r))}return e}createStructOffsetList(t,e){return e(this,t.length),this.createObjectOffsetList(t.slice().reverse()),this.endVector()}};var hr,dr;!function(t){t[t.BUFFER=0]="BUFFER"}(hr||(hr={})),function(t){t[t.LZ4_FRAME=0]="LZ4_FRAME",t[t.ZSTD=1]="ZSTD"}(dr||(dr={}));class Ke{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBodyCompression(t,e){return(e||new Ke).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBodyCompression(t,e){return t.setPosition(t.position()+4),(e||new Ke).__init(t.readInt32(t.position())+t.position(),t)}codec(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt8(this.bb_pos+t):dr.LZ4_FRAME}method(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt8(this.bb_pos+t):hr.BUFFER}static startBodyCompression(t){t.startObject(2)}static addCodec(t,e){t.addFieldInt8(0,e,dr.LZ4_FRAME)}static addMethod(t,e){t.addFieldInt8(1,e,hr.BUFFER)}static endBodyCompression(t){return t.endObject()}static createBodyCompression(t,e,i){return Ke.startBodyCompression(t),Ke.addCodec(t,e),Ke.addMethod(t,i),Ke.endBodyCompression(t)}}class Na{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}offset(){return this.bb.readInt64(this.bb_pos)}length(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createBuffer(t,e,i){return t.prep(8,16),t.writeInt64(BigInt(i??0)),t.writeInt64(BigInt(e??0)),t.offset()}}let Ba=class{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}length(){return this.bb.readInt64(this.bb_pos)}nullCount(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createFieldNode(t,e,i){return t.prep(8,16),t.writeInt64(BigInt(i??0)),t.writeInt64(BigInt(e??0)),t.offset()}},Ce=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsRecordBatch(e,i){return(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsRecordBatch(e,i){return e.setPosition(e.position()+4),(i||new t).__init(e.readInt32(e.position())+e.position(),e)}length(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}nodes(t,e){const i=this.bb.__offset(this.bb_pos,6);return i?(e||new Ba).__init(this.bb.__vector(this.bb_pos+i)+16*t,this.bb):null}nodesLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}buffers(t,e){const i=this.bb.__offset(this.bb_pos,8);return i?(e||new Na).__init(this.bb.__vector(this.bb_pos+i)+16*t,this.bb):null}buffersLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}compression(t){const e=this.bb.__offset(this.bb_pos,10);return e?(t||new Ke).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}static startRecordBatch(t){t.startObject(4)}static addLength(t,e){t.addFieldInt64(0,e,BigInt("0"))}static addNodes(t,e){t.addFieldOffset(1,e,0)}static startNodesVector(t,e){t.startVector(16,e,8)}static addBuffers(t,e){t.addFieldOffset(2,e,0)}static startBuffersVector(t,e){t.startVector(16,e,8)}static addCompression(t,e){t.addFieldOffset(3,e,0)}static endRecordBatch(t){return t.endObject()}},Fn=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDictionaryBatch(e,i){return(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsDictionaryBatch(e,i){return e.setPosition(e.position()+4),(i||new t).__init(e.readInt32(e.position())+e.position(),e)}id(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}data(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new Ce).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}isDelta(){const t=this.bb.__offset(this.bb_pos,8);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startDictionaryBatch(t){t.startObject(3)}static addId(t,e){t.addFieldInt64(0,e,BigInt("0"))}static addData(t,e){t.addFieldOffset(1,e,0)}static addIsDelta(t,e){t.addFieldInt8(2,+e,0)}static endDictionaryBatch(t){return t.endObject()}};var Kn,fr;!function(t){t[t.Little=0]="Little",t[t.Big=1]="Big"}(Kn||(Kn={})),function(t){t[t.DenseArray=0]="DenseArray"}(fr||(fr={}));class te{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInt(t,e){return(e||new te).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInt(t,e){return t.setPosition(t.position()+4),(e||new te).__init(t.readInt32(t.position())+t.position(),t)}bitWidth(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}isSigned(){const t=this.bb.__offset(this.bb_pos,6);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startInt(t){t.startObject(2)}static addBitWidth(t,e){t.addFieldInt32(0,e,0)}static addIsSigned(t,e){t.addFieldInt8(1,+e,0)}static endInt(t){return t.endObject()}static createInt(t,e,i){return te.startInt(t),te.addBitWidth(t,e),te.addIsSigned(t,i),te.endInt(t)}}class je{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDictionaryEncoding(t,e){return(e||new je).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDictionaryEncoding(t,e){return t.setPosition(t.position()+4),(e||new je).__init(t.readInt32(t.position())+t.position(),t)}id(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}indexType(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new te).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}isOrdered(){const t=this.bb.__offset(this.bb_pos,8);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}dictionaryKind(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt16(this.bb_pos+t):fr.DenseArray}static startDictionaryEncoding(t){t.startObject(4)}static addId(t,e){t.addFieldInt64(0,e,BigInt("0"))}static addIndexType(t,e){t.addFieldOffset(1,e,0)}static addIsOrdered(t,e){t.addFieldInt8(2,+e,0)}static addDictionaryKind(t,e){t.addFieldInt16(3,e,fr.DenseArray)}static endDictionaryEncoding(t){return t.endObject()}}class Mt{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsKeyValue(t,e){return(e||new Mt).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsKeyValue(t,e){return t.setPosition(t.position()+4),(e||new Mt).__init(t.readInt32(t.position())+t.position(),t)}key(t){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}value(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startKeyValue(t){t.startObject(2)}static addKey(t,e){t.addFieldOffset(0,e,0)}static addValue(t,e){t.addFieldOffset(1,e,0)}static endKeyValue(t){return t.endObject()}static createKeyValue(t,e,i){return Mt.startKeyValue(t),Mt.addKey(t,e),Mt.addValue(t,i),Mt.endKeyValue(t)}}let ko=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBinary(e,i){return(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsBinary(e,i){return e.setPosition(e.position()+4),(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static startBinary(t){t.startObject(0)}static endBinary(t){return t.endObject()}static createBinary(e){return t.startBinary(e),t.endBinary(e)}},$o=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBool(e,i){return(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsBool(e,i){return e.setPosition(e.position()+4),(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static startBool(t){t.startObject(0)}static endBool(t){return t.endObject()}static createBool(e){return t.startBool(e),t.endBool(e)}},tr=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDate(e,i){return(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsDate(e,i){return e.setPosition(e.position()+4),(i||new t).__init(e.readInt32(e.position())+e.position(),e)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):le.MILLISECOND}static startDate(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,le.MILLISECOND)}static endDate(t){return t.endObject()}static createDate(e,i){return t.startDate(e),t.addUnit(e,i),t.endDate(e)}},Dn=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDecimal(e,i){return(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsDecimal(e,i){return e.setPosition(e.position()+4),(i||new t).__init(e.readInt32(e.position())+e.position(),e)}precision(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}scale(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):0}bitWidth(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readInt32(this.bb_pos+t):128}static startDecimal(t){t.startObject(3)}static addPrecision(t,e){t.addFieldInt32(0,e,0)}static addScale(t,e){t.addFieldInt32(1,e,0)}static addBitWidth(t,e){t.addFieldInt32(2,e,128)}static endDecimal(t){return t.endObject()}static createDecimal(e,i,r,n){return t.startDecimal(e),t.addPrecision(e,i),t.addScale(e,r),t.addBitWidth(e,n),t.endDecimal(e)}},er=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDuration(e,i){return(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsDuration(e,i){return e.setPosition(e.position()+4),(i||new t).__init(e.readInt32(e.position())+e.position(),e)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):x.MILLISECOND}static startDuration(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,x.MILLISECOND)}static endDuration(t){return t.endObject()}static createDuration(e,i){return t.startDuration(e),t.addUnit(e,i),t.endDuration(e)}},nr=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFixedSizeBinary(e,i){return(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsFixedSizeBinary(e,i){return e.setPosition(e.position()+4),(i||new t).__init(e.readInt32(e.position())+e.position(),e)}byteWidth(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeBinary(t){t.startObject(1)}static addByteWidth(t,e){t.addFieldInt32(0,e,0)}static endFixedSizeBinary(t){return t.endObject()}static createFixedSizeBinary(e,i){return t.startFixedSizeBinary(e),t.addByteWidth(e,i),t.endFixedSizeBinary(e)}},ir=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFixedSizeList(e,i){return(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsFixedSizeList(e,i){return e.setPosition(e.position()+4),(i||new t).__init(e.readInt32(e.position())+e.position(),e)}listSize(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeList(t){t.startObject(1)}static addListSize(t,e){t.addFieldInt32(0,e,0)}static endFixedSizeList(t){return t.endObject()}static createFixedSizeList(e,i){return t.startFixedSizeList(e),t.addListSize(e,i),t.endFixedSizeList(e)}};class Ee{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFloatingPoint(t,e){return(e||new Ee).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFloatingPoint(t,e){return t.setPosition(t.position()+4),(e||new Ee).__init(t.readInt32(t.position())+t.position(),t)}precision(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):xt.HALF}static startFloatingPoint(t){t.startObject(1)}static addPrecision(t,e){t.addFieldInt16(0,e,xt.HALF)}static endFloatingPoint(t){return t.endObject()}static createFloatingPoint(t,e){return Ee.startFloatingPoint(t),Ee.addPrecision(t,e),Ee.endFloatingPoint(t)}}class Te{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInterval(t,e){return(e||new Te).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInterval(t,e){return t.setPosition(t.position()+4),(e||new Te).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):De.YEAR_MONTH}static startInterval(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,De.YEAR_MONTH)}static endInterval(t){return t.endObject()}static createInterval(t,e){return Te.startInterval(t),Te.addUnit(t,e),Te.endInterval(t)}}let jo=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsLargeBinary(e,i){return(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsLargeBinary(e,i){return e.setPosition(e.position()+4),(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static startLargeBinary(t){t.startObject(0)}static endLargeBinary(t){return t.endObject()}static createLargeBinary(e){return t.startLargeBinary(e),t.endLargeBinary(e)}},Vo=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsLargeUtf8(e,i){return(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsLargeUtf8(e,i){return e.setPosition(e.position()+4),(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static startLargeUtf8(t){t.startObject(0)}static endLargeUtf8(t){return t.endObject()}static createLargeUtf8(e){return t.startLargeUtf8(e),t.endLargeUtf8(e)}},zo=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsList(e,i){return(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsList(e,i){return e.setPosition(e.position()+4),(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static startList(t){t.startObject(0)}static endList(t){return t.endObject()}static createList(e){return t.startList(e),t.endList(e)}},rr=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMap(e,i){return(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsMap(e,i){return e.setPosition(e.position()+4),(i||new t).__init(e.readInt32(e.position())+e.position(),e)}keysSorted(){const t=this.bb.__offset(this.bb_pos,4);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startMap(t){t.startObject(1)}static addKeysSorted(t,e){t.addFieldInt8(0,+e,0)}static endMap(t){return t.endObject()}static createMap(e,i){return t.startMap(e),t.addKeysSorted(e,i),t.endMap(e)}},Wo=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsNull(e,i){return(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsNull(e,i){return e.setPosition(e.position()+4),(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static startNull(t){t.startObject(0)}static endNull(t){return t.endObject()}static createNull(e){return t.startNull(e),t.endNull(e)}};class pn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsStruct_(t,e){return(e||new pn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsStruct_(t,e){return t.setPosition(t.position()+4),(e||new pn).__init(t.readInt32(t.position())+t.position(),t)}static startStruct_(t){t.startObject(0)}static endStruct_(t){return t.endObject()}static createStruct_(t){return pn.startStruct_(t),pn.endStruct_(t)}}class oe{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTime(t,e){return(e||new oe).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTime(t,e){return t.setPosition(t.position()+4),(e||new oe).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):x.MILLISECOND}bitWidth(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):32}static startTime(t){t.startObject(2)}static addUnit(t,e){t.addFieldInt16(0,e,x.MILLISECOND)}static addBitWidth(t,e){t.addFieldInt32(1,e,32)}static endTime(t){return t.endObject()}static createTime(t,e,i){return oe.startTime(t),oe.addUnit(t,e),oe.addBitWidth(t,i),oe.endTime(t)}}class ae{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTimestamp(t,e){return(e||new ae).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTimestamp(t,e){return t.setPosition(t.position()+4),(e||new ae).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):x.SECOND}timezone(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startTimestamp(t){t.startObject(2)}static addUnit(t,e){t.addFieldInt16(0,e,x.SECOND)}static addTimezone(t,e){t.addFieldOffset(1,e,0)}static endTimestamp(t){return t.endObject()}static createTimestamp(t,e,i){return ae.startTimestamp(t),ae.addUnit(t,e),ae.addTimezone(t,i),ae.endTimestamp(t)}}class Ht{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsUnion(t,e){return(e||new Ht).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsUnion(t,e){return t.setPosition(t.position()+4),(e||new Ht).__init(t.readInt32(t.position())+t.position(),t)}mode(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Pt.Sparse}typeIds(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readInt32(this.bb.__vector(this.bb_pos+e)+4*t):0}typeIdsLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}typeIdsArray(){const t=this.bb.__offset(this.bb_pos,6);return t?new Int32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}static startUnion(t){t.startObject(2)}static addMode(t,e){t.addFieldInt16(0,e,Pt.Sparse)}static addTypeIds(t,e){t.addFieldOffset(1,e,0)}static createTypeIdsVector(t,e){t.startVector(4,e.length,4);for(let i=e.length-1;i>=0;i--)t.addInt32(e[i]);return t.endVector()}static startTypeIdsVector(t,e){t.startVector(4,e,4)}static endUnion(t){return t.endObject()}static createUnion(t,e,i){return Ht.startUnion(t),Ht.addMode(t,e),Ht.addTypeIds(t,i),Ht.endUnion(t)}}let Yo=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsUtf8(e,i){return(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsUtf8(e,i){return e.setPosition(e.position()+4),(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static startUtf8(t){t.startObject(0)}static endUtf8(t){return t.endObject()}static createUtf8(e){return t.startUtf8(e),t.endUtf8(e)}};var mt;!function(t){t[t.NONE=0]="NONE",t[t.Null=1]="Null",t[t.Int=2]="Int",t[t.FloatingPoint=3]="FloatingPoint",t[t.Binary=4]="Binary",t[t.Utf8=5]="Utf8",t[t.Bool=6]="Bool",t[t.Decimal=7]="Decimal",t[t.Date=8]="Date",t[t.Time=9]="Time",t[t.Timestamp=10]="Timestamp",t[t.Interval=11]="Interval",t[t.List=12]="List",t[t.Struct_=13]="Struct_",t[t.Union=14]="Union",t[t.FixedSizeBinary=15]="FixedSizeBinary",t[t.FixedSizeList=16]="FixedSizeList",t[t.Map=17]="Map",t[t.Duration=18]="Duration",t[t.LargeBinary=19]="LargeBinary",t[t.LargeUtf8=20]="LargeUtf8",t[t.LargeList=21]="LargeList",t[t.RunEndEncoded=22]="RunEndEncoded"}(mt||(mt={}));let ne=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsField(e,i){return(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsField(e,i){return e.setPosition(e.position()+4),(i||new t).__init(e.readInt32(e.position())+e.position(),e)}name(t){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}nullable(){const t=this.bb.__offset(this.bb_pos,6);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}typeType(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readUint8(this.bb_pos+t):mt.NONE}type(t){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.__union(t,this.bb_pos+e):null}dictionary(t){const e=this.bb.__offset(this.bb_pos,12);return e?(t||new je).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}children(e,i){const r=this.bb.__offset(this.bb_pos,14);return r?(i||new t).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*e),this.bb):null}childrenLength(){const t=this.bb.__offset(this.bb_pos,14);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const i=this.bb.__offset(this.bb_pos,16);return i?(e||new Mt).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+i)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,16);return t?this.bb.__vector_len(this.bb_pos+t):0}static startField(t){t.startObject(7)}static addName(t,e){t.addFieldOffset(0,e,0)}static addNullable(t,e){t.addFieldInt8(1,+e,0)}static addTypeType(t,e){t.addFieldInt8(2,e,mt.NONE)}static addType(t,e){t.addFieldOffset(3,e,0)}static addDictionary(t,e){t.addFieldOffset(4,e,0)}static addChildren(t,e){t.addFieldOffset(5,e,0)}static createChildrenVector(t,e){t.startVector(4,e.length,4);for(let i=e.length-1;i>=0;i--)t.addOffset(e[i]);return t.endVector()}static startChildrenVector(t,e){t.startVector(4,e,4)}static addCustomMetadata(t,e){t.addFieldOffset(6,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let i=e.length-1;i>=0;i--)t.addOffset(e[i]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endField(t){return t.endObject()}},Ie=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsSchema(e,i){return(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsSchema(e,i){return e.setPosition(e.position()+4),(i||new t).__init(e.readInt32(e.position())+e.position(),e)}endianness(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Kn.Little}fields(t,e){const i=this.bb.__offset(this.bb_pos,6);return i?(e||new ne).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+i)+4*t),this.bb):null}fieldsLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const i=this.bb.__offset(this.bb_pos,8);return i?(e||new Mt).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+i)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}features(t){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.readInt64(this.bb.__vector(this.bb_pos+e)+8*t):BigInt(0)}featuresLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}static startSchema(t){t.startObject(4)}static addEndianness(t,e){t.addFieldInt16(0,e,Kn.Little)}static addFields(t,e){t.addFieldOffset(1,e,0)}static createFieldsVector(t,e){t.startVector(4,e.length,4);for(let i=e.length-1;i>=0;i--)t.addOffset(e[i]);return t.endVector()}static startFieldsVector(t,e){t.startVector(4,e,4)}static addCustomMetadata(t,e){t.addFieldOffset(2,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let i=e.length-1;i>=0;i--)t.addOffset(e[i]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static addFeatures(t,e){t.addFieldOffset(3,e,0)}static createFeaturesVector(t,e){t.startVector(8,e.length,8);for(let i=e.length-1;i>=0;i--)t.addInt64(e[i]);return t.endVector()}static startFeaturesVector(t,e){t.startVector(8,e,8)}static endSchema(t){return t.endObject()}static finishSchemaBuffer(t,e){t.finish(e)}static finishSizePrefixedSchemaBuffer(t,e){t.finish(e,void 0,!0)}static createSchema(e,i,r,n,s){return t.startSchema(e),t.addEndianness(e,i),t.addFields(e,r),t.addCustomMetadata(e,n),t.addFeatures(e,s),t.endSchema(e)}};var at,h,Le;!function(t){t[t.NONE=0]="NONE",t[t.Schema=1]="Schema",t[t.DictionaryBatch=2]="DictionaryBatch",t[t.RecordBatch=3]="RecordBatch",t[t.Tensor=4]="Tensor",t[t.SparseTensor=5]="SparseTensor"}(at||(at={})),function(t){t[t.NONE=0]="NONE",t[t.Null=1]="Null",t[t.Int=2]="Int",t[t.Float=3]="Float",t[t.Binary=4]="Binary",t[t.Utf8=5]="Utf8",t[t.Bool=6]="Bool",t[t.Decimal=7]="Decimal",t[t.Date=8]="Date",t[t.Time=9]="Time",t[t.Timestamp=10]="Timestamp",t[t.Interval=11]="Interval",t[t.List=12]="List",t[t.Struct=13]="Struct",t[t.Union=14]="Union",t[t.FixedSizeBinary=15]="FixedSizeBinary",t[t.FixedSizeList=16]="FixedSizeList",t[t.Map=17]="Map",t[t.Duration=18]="Duration",t[t.LargeBinary=19]="LargeBinary",t[t.LargeUtf8=20]="LargeUtf8",t[t.Dictionary=-1]="Dictionary",t[t.Int8=-2]="Int8",t[t.Int16=-3]="Int16",t[t.Int32=-4]="Int32",t[t.Int64=-5]="Int64",t[t.Uint8=-6]="Uint8",t[t.Uint16=-7]="Uint16",t[t.Uint32=-8]="Uint32",t[t.Uint64=-9]="Uint64",t[t.Float16=-10]="Float16",t[t.Float32=-11]="Float32",t[t.Float64=-12]="Float64",t[t.DateDay=-13]="DateDay",t[t.DateMillisecond=-14]="DateMillisecond",t[t.TimestampSecond=-15]="TimestampSecond",t[t.TimestampMillisecond=-16]="TimestampMillisecond",t[t.TimestampMicrosecond=-17]="TimestampMicrosecond",t[t.TimestampNanosecond=-18]="TimestampNanosecond",t[t.TimeSecond=-19]="TimeSecond",t[t.TimeMillisecond=-20]="TimeMillisecond",t[t.TimeMicrosecond=-21]="TimeMicrosecond",t[t.TimeNanosecond=-22]="TimeNanosecond",t[t.DenseUnion=-23]="DenseUnion",t[t.SparseUnion=-24]="SparseUnion",t[t.IntervalDayTime=-25]="IntervalDayTime",t[t.IntervalYearMonth=-26]="IntervalYearMonth",t[t.DurationSecond=-27]="DurationSecond",t[t.DurationMillisecond=-28]="DurationMillisecond",t[t.DurationMicrosecond=-29]="DurationMicrosecond",t[t.DurationNanosecond=-30]="DurationNanosecond"}(h||(h={})),function(t){t[t.OFFSET=0]="OFFSET",t[t.DATA=1]="DATA",t[t.VALIDITY=2]="VALIDITY",t[t.TYPE=3]="TYPE"}(Le||(Le={}));const ou=void 0;function vi(t){if(null===t)return"null";if(t===ou)return"undefined";switch(typeof t){case"number":case"bigint":return`${t}`;case"string":return`"${t}"`}return"function"==typeof t[Symbol.toPrimitive]?t[Symbol.toPrimitive]("string"):ArrayBuffer.isView(t)?t instanceof BigInt64Array||t instanceof BigUint64Array?`[${[...t].map((t=>vi(t)))}]`:`[${t}]`:ArrayBuffer.isView(t)?`[${t}]`:JSON.stringify(t,((t,e)=>"bigint"==typeof e?`${e}`:e))}function pt(t){if("bigint"==typeof t&&(t<Number.MIN_SAFE_INTEGER||t>Number.MAX_SAFE_INTEGER))throw new TypeError(`${t} is not safe to convert to a number.`);return Number(t)}function Ra(t,e){return pt(t/e)+pt(t%e)/pt(e)}const au=Symbol.for("isArrowBigNum");function be(t,...e){return 0===e.length?Object.setPrototypeOf(ft(this.TypedArray,t),this.constructor.prototype):Object.setPrototypeOf(new this.TypedArray(t,...e),this.constructor.prototype)}function kn(...t){return be.apply(this,t)}function $n(...t){return be.apply(this,t)}function Si(...t){return be.apply(this,t)}be.prototype[au]=!0,be.prototype.toJSON=function(){return`"${Ei(this)}"`},be.prototype.valueOf=function(t){return Ma(this,t)},be.prototype.toString=function(){return Ei(this)},be.prototype[Symbol.toPrimitive]=function(t="default"){switch(t){case"number":return Ma(this);case"string":return Ei(this);case"default":return uu(this)}return Ei(this)},Object.setPrototypeOf(kn.prototype,Object.create(Int32Array.prototype)),Object.setPrototypeOf($n.prototype,Object.create(Uint32Array.prototype)),Object.setPrototypeOf(Si.prototype,Object.create(Uint32Array.prototype)),Object.assign(kn.prototype,be.prototype,{constructor:kn,signed:!0,TypedArray:Int32Array,BigIntArray:BigInt64Array}),Object.assign($n.prototype,be.prototype,{constructor:$n,signed:!1,TypedArray:Uint32Array,BigIntArray:BigUint64Array}),Object.assign(Si.prototype,be.prototype,{constructor:Si,signed:!0,TypedArray:Uint32Array,BigIntArray:BigUint64Array});const cu=BigInt(4294967296)*BigInt(4294967296),lu=cu-BigInt(1);function Ma(t,e){const{buffer:i,byteOffset:r,byteLength:n,signed:s}=t,o=new BigUint64Array(i,r,n/8),a=s&&o.at(-1)&BigInt(1)<<BigInt(63);let l=BigInt(0),c=0;if(a){for(const t of o)l|=(t^lu)*(BigInt(1)<<BigInt(64*c++));l*=BigInt(-1),l-=BigInt(1)}else for(const t of o)l|=t*(BigInt(1)<<BigInt(64*c++));if("number"==typeof e){const t=BigInt(Math.pow(10,e)),i=l%t;return pt(l/t)+pt(i)/pt(t)}return pt(l)}function Ei(t){if(8===t.byteLength)return`${new t.BigIntArray(t.buffer,t.byteOffset,1)[0]}`;if(!t.signed)return as(t);let e=new Uint16Array(t.buffer,t.byteOffset,t.byteLength/2);if(new Int16Array([e.at(-1)])[0]>=0)return as(t);e=e.slice();let i=1;for(let t=0;t<e.length;t++){const r=e[t],n=~r+i;e[t]=n,i&=0===r?1:0}return`-${as(e)}`}function uu(t){return 8===t.byteLength?new t.BigIntArray(t.buffer,t.byteOffset,1)[0]:Ei(t)}function as(t){let e="";const i=new Uint32Array(2);let r=new Uint16Array(t.buffer,t.byteOffset,t.byteLength/2);const n=new Uint32Array((r=new Uint16Array(r).reverse()).buffer);let s=-1;const o=r.length-1;do{for(i[0]=r[s=0];s<o;)r[s++]=i[1]=i[0]/10,i[0]=(i[0]-10*i[1]<<16)+r[s];r[s]=i[1]=i[0]/10,i[0]=i[0]-10*i[1],e=`${i[0]}${e}`}while(n[0]||n[1]||n[2]||n[3]);return e??"0"}class zs{static new(t,e){switch(e){case!0:return new kn(t);case!1:return new $n(t)}switch(t.constructor){case Int8Array:case Int16Array:case Int32Array:case BigInt64Array:return new kn(t)}return 16===t.byteLength?new Si(t):new $n(t)}static signed(t){return new kn(t)}static unsigned(t){return new $n(t)}static decimal(t){return new Si(t)}constructor(t,e){return zs.new(t,e)}}var Ca,La,Ua,xa,Pa,ka,$a,ja,Va,za,Wa,Ya,Ga,qa,Ha,Qa,Ja,Ka,Za,Xa,tc,ec;class D{static isNull(t){return(null==t?void 0:t.typeId)===h.Null}static isInt(t){return(null==t?void 0:t.typeId)===h.Int}static isFloat(t){return(null==t?void 0:t.typeId)===h.Float}static isBinary(t){return(null==t?void 0:t.typeId)===h.Binary}static isLargeBinary(t){return(null==t?void 0:t.typeId)===h.LargeBinary}static isUtf8(t){return(null==t?void 0:t.typeId)===h.Utf8}static isLargeUtf8(t){return(null==t?void 0:t.typeId)===h.LargeUtf8}static isBool(t){return(null==t?void 0:t.typeId)===h.Bool}static isDecimal(t){return(null==t?void 0:t.typeId)===h.Decimal}static isDate(t){return(null==t?void 0:t.typeId)===h.Date}static isTime(t){return(null==t?void 0:t.typeId)===h.Time}static isTimestamp(t){return(null==t?void 0:t.typeId)===h.Timestamp}static isInterval(t){return(null==t?void 0:t.typeId)===h.Interval}static isDuration(t){return(null==t?void 0:t.typeId)===h.Duration}static isList(t){return(null==t?void 0:t.typeId)===h.List}static isStruct(t){return(null==t?void 0:t.typeId)===h.Struct}static isUnion(t){return(null==t?void 0:t.typeId)===h.Union}static isFixedSizeBinary(t){return(null==t?void 0:t.typeId)===h.FixedSizeBinary}static isFixedSizeList(t){return(null==t?void 0:t.typeId)===h.FixedSizeList}static isMap(t){return(null==t?void 0:t.typeId)===h.Map}static isDictionary(t){return(null==t?void 0:t.typeId)===h.Dictionary}static isDenseUnion(t){return D.isUnion(t)&&t.mode===Pt.Dense}static isSparseUnion(t){return D.isUnion(t)&&t.mode===Pt.Sparse}constructor(t){this.typeId=t}}Ca=Symbol.toStringTag,D[Ca]=(t=>(t.children=null,t.ArrayType=Array,t.OffsetArrayType=Int32Array,t[Symbol.toStringTag]="DataType"))(D.prototype);class rn extends D{constructor(){super(h.Null)}toString(){return"Null"}}La=Symbol.toStringTag,rn[La]=rn.prototype[Symbol.toStringTag]="Null";class En extends D{constructor(t,e){super(h.Int),this.isSigned=t,this.bitWidth=e}get ArrayType(){switch(this.bitWidth){case 8:return this.isSigned?Int8Array:Uint8Array;case 16:return this.isSigned?Int16Array:Uint16Array;case 32:return this.isSigned?Int32Array:Uint32Array;case 64:return this.isSigned?BigInt64Array:BigUint64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}toString(){return`${this.isSigned?"I":"Ui"}nt${this.bitWidth}`}}Ua=Symbol.toStringTag,En[Ua]=(t=>(t.isSigned=null,t.bitWidth=null,t[Symbol.toStringTag]="Int"))(En.prototype);class Ti extends En{constructor(){super(!0,32)}get ArrayType(){return Int32Array}}Object.defineProperty(Ti.prototype,"ArrayType",{value:Int32Array});class pr extends D{constructor(t){super(h.Float),this.precision=t}get ArrayType(){switch(this.precision){case xt.HALF:return Uint16Array;case xt.SINGLE:return Float32Array;case xt.DOUBLE:return Float64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}toString(){return`Float${this.precision<<5||16}`}}xa=Symbol.toStringTag,pr[xa]=(t=>(t.precision=null,t[Symbol.toStringTag]="Float"))(pr.prototype);class yr extends D{constructor(){super(h.Binary)}toString(){return"Binary"}}Pa=Symbol.toStringTag,yr[Pa]=(t=>(t.ArrayType=Uint8Array,t[Symbol.toStringTag]="Binary"))(yr.prototype);class mr extends D{constructor(){super(h.LargeBinary)}toString(){return"LargeBinary"}}ka=Symbol.toStringTag,mr[ka]=(t=>(t.ArrayType=Uint8Array,t.OffsetArrayType=BigInt64Array,t[Symbol.toStringTag]="LargeBinary"))(mr.prototype);class gr extends D{constructor(){super(h.Utf8)}toString(){return"Utf8"}}$a=Symbol.toStringTag,gr[$a]=(t=>(t.ArrayType=Uint8Array,t[Symbol.toStringTag]="Utf8"))(gr.prototype);class br extends D{constructor(){super(h.LargeUtf8)}toString(){return"LargeUtf8"}}ja=Symbol.toStringTag,br[ja]=(t=>(t.ArrayType=Uint8Array,t.OffsetArrayType=BigInt64Array,t[Symbol.toStringTag]="LargeUtf8"))(br.prototype);class _r extends D{constructor(){super(h.Bool)}toString(){return"Bool"}}Va=Symbol.toStringTag,_r[Va]=(t=>(t.ArrayType=Uint8Array,t[Symbol.toStringTag]="Bool"))(_r.prototype);class wr extends D{constructor(t,e,i=128){super(h.Decimal),this.scale=t,this.precision=e,this.bitWidth=i}toString(){return`Decimal[${this.precision}e${this.scale>0?"+":""}${this.scale}]`}}za=Symbol.toStringTag,wr[za]=(t=>(t.scale=null,t.precision=null,t.ArrayType=Uint32Array,t[Symbol.toStringTag]="Decimal"))(wr.prototype);class Ir extends D{constructor(t){super(h.Date),this.unit=t}toString(){return`Date${32*(this.unit+1)}<${le[this.unit]}>`}get ArrayType(){return this.unit===le.DAY?Int32Array:BigInt64Array}}Wa=Symbol.toStringTag,Ir[Wa]=(t=>(t.unit=null,t[Symbol.toStringTag]="Date"))(Ir.prototype);class vr extends D{constructor(t,e){super(h.Time),this.unit=t,this.bitWidth=e}toString(){return`Time${this.bitWidth}<${x[this.unit]}>`}get ArrayType(){switch(this.bitWidth){case 32:return Int32Array;case 64:return BigInt64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}}Ya=Symbol.toStringTag,vr[Ya]=(t=>(t.unit=null,t.bitWidth=null,t[Symbol.toStringTag]="Time"))(vr.prototype);class Sr extends D{constructor(t,e){super(h.Timestamp),this.unit=t,this.timezone=e}toString(){return`Timestamp<${x[this.unit]}${this.timezone?`, ${this.timezone}`:""}>`}}Ga=Symbol.toStringTag,Sr[Ga]=(t=>(t.unit=null,t.timezone=null,t.ArrayType=BigInt64Array,t[Symbol.toStringTag]="Timestamp"))(Sr.prototype);class Er extends D{constructor(t){super(h.Interval),this.unit=t}toString(){return`Interval<${De[this.unit]}>`}}qa=Symbol.toStringTag,Er[qa]=(t=>(t.unit=null,t.ArrayType=Int32Array,t[Symbol.toStringTag]="Interval"))(Er.prototype);class Tr extends D{constructor(t){super(h.Duration),this.unit=t}toString(){return`Duration<${x[this.unit]}>`}}Ha=Symbol.toStringTag,Tr[Ha]=(t=>(t.unit=null,t.ArrayType=BigInt64Array,t[Symbol.toStringTag]="Duration"))(Tr.prototype);class Ar extends D{constructor(t){super(h.List),this.children=[t]}toString(){return`List<${this.valueType}>`}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}}Qa=Symbol.toStringTag,Ar[Qa]=(t=>(t.children=null,t[Symbol.toStringTag]="List"))(Ar.prototype);class zt extends D{constructor(t){super(h.Struct),this.children=t}toString(){return`Struct<{${this.children.map((t=>`${t.name}:${t.type}`)).join(", ")}}>`}}Ja=Symbol.toStringTag,zt[Ja]=(t=>(t.children=null,t[Symbol.toStringTag]="Struct"))(zt.prototype);class Fr extends D{constructor(t,e,i){super(h.Union),this.mode=t,this.children=i,this.typeIds=e=Int32Array.from(e),this.typeIdToChildIndex=e.reduce(((t,e,i)=>(t[e]=i)&&t||t),Object.create(null))}toString(){return`${this[Symbol.toStringTag]}<${this.children.map((t=>`${t.type}`)).join(" | ")}>`}}Ka=Symbol.toStringTag,Fr[Ka]=(t=>(t.mode=null,t.typeIds=null,t.children=null,t.typeIdToChildIndex=null,t.ArrayType=Int8Array,t[Symbol.toStringTag]="Union"))(Fr.prototype);class Or extends D{constructor(t){super(h.FixedSizeBinary),this.byteWidth=t}toString(){return`FixedSizeBinary[${this.byteWidth}]`}}Za=Symbol.toStringTag,Or[Za]=(t=>(t.byteWidth=null,t.ArrayType=Uint8Array,t[Symbol.toStringTag]="FixedSizeBinary"))(Or.prototype);class Dr extends D{constructor(t,e){super(h.FixedSizeList),this.listSize=t,this.children=[e]}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}toString(){return`FixedSizeList[${this.listSize}]<${this.valueType}>`}}Xa=Symbol.toStringTag,Dr[Xa]=(t=>(t.children=null,t.listSize=null,t[Symbol.toStringTag]="FixedSizeList"))(Dr.prototype);class Nr extends D{constructor(t,e=!1){var i,r,n;if(super(h.Map),this.children=[t],this.keysSorted=e,t&&(t.name="entries",null!==(i=null==t?void 0:t.type)&&void 0!==i&&i.children)){const e=null===(r=null==t?void 0:t.type)||void 0===r?void 0:r.children[0];e&&(e.name="key");const i=null===(n=null==t?void 0:t.type)||void 0===n?void 0:n.children[1];i&&(i.name="value")}}get keyType(){return this.children[0].type.children[0].type}get valueType(){return this.children[0].type.children[1].type}get childType(){return this.children[0].type}toString(){return`Map<{${this.children[0].type.children.map((t=>`${t.name}:${t.type}`)).join(", ")}}>`}}tc=Symbol.toStringTag,Nr[tc]=(t=>(t.children=null,t.keysSorted=null,t[Symbol.toStringTag]="Map_"))(Nr.prototype);const hu=(t=>()=>++t)(-1);class Zn extends D{constructor(t,e,i,r){super(h.Dictionary),this.indices=e,this.dictionary=t,this.isOrdered=r||!1,this.id=null==i?hu():pt(i)}get children(){return this.dictionary.children}get valueType(){return this.dictionary}get ArrayType(){return this.dictionary.ArrayType}toString(){return`Dictionary<${this.indices}, ${this.dictionary}>`}}function Ue(t){const e=t;switch(t.typeId){case h.Decimal:return t.bitWidth/32;case h.Interval:return 1+e.unit;case h.FixedSizeList:return e.listSize;case h.FixedSizeBinary:return e.byteWidth;default:return 1}}ec=Symbol.toStringTag,Zn[ec]=(t=>(t.id=null,t.indices=null,t.isOrdered=null,t.dictionary=null,t[Symbol.toStringTag]="Dictionary"))(Zn.prototype);class J{visitMany(t,...e){return t.map(((t,i)=>this.visit(t,...e.map((t=>t[i])))))}visit(...t){return this.getVisitFn(t[0],!1).apply(this,t)}getVisitFn(t,e=!0){return du(this,t,e)}getVisitFnByTypeId(t,e=!0){return Cn(this,t,e)}visitNull(t,...e){return null}visitBool(t,...e){return null}visitInt(t,...e){return null}visitFloat(t,...e){return null}visitUtf8(t,...e){return null}visitLargeUtf8(t,...e){return null}visitBinary(t,...e){return null}visitLargeBinary(t,...e){return null}visitFixedSizeBinary(t,...e){return null}visitDate(t,...e){return null}visitTimestamp(t,...e){return null}visitTime(t,...e){return null}visitDecimal(t,...e){return null}visitList(t,...e){return null}visitStruct(t,...e){return null}visitUnion(t,...e){return null}visitDictionary(t,...e){return null}visitInterval(t,...e){return null}visitDuration(t,...e){return null}visitFixedSizeList(t,...e){return null}visitMap(t,...e){return null}}function du(t,e,i=!0){return"number"==typeof e?Cn(t,e,i):"string"==typeof e&&e in h?Cn(t,h[e],i):e&&e instanceof D?Cn(t,Go(e),i):null!=e&&e.type&&e.type instanceof D?Cn(t,Go(e.type),i):Cn(t,h.NONE,i)}function Cn(t,e,i=!0){let r=null;switch(e){case h.Null:r=t.visitNull;break;case h.Bool:r=t.visitBool;break;case h.Int:r=t.visitInt;break;case h.Int8:r=t.visitInt8||t.visitInt;break;case h.Int16:r=t.visitInt16||t.visitInt;break;case h.Int32:r=t.visitInt32||t.visitInt;break;case h.Int64:r=t.visitInt64||t.visitInt;break;case h.Uint8:r=t.visitUint8||t.visitInt;break;case h.Uint16:r=t.visitUint16||t.visitInt;break;case h.Uint32:r=t.visitUint32||t.visitInt;break;case h.Uint64:r=t.visitUint64||t.visitInt;break;case h.Float:r=t.visitFloat;break;case h.Float16:r=t.visitFloat16||t.visitFloat;break;case h.Float32:r=t.visitFloat32||t.visitFloat;break;case h.Float64:r=t.visitFloat64||t.visitFloat;break;case h.Utf8:r=t.visitUtf8;break;case h.LargeUtf8:r=t.visitLargeUtf8;break;case h.Binary:r=t.visitBinary;break;case h.LargeBinary:r=t.visitLargeBinary;break;case h.FixedSizeBinary:r=t.visitFixedSizeBinary;break;case h.Date:r=t.visitDate;break;case h.DateDay:r=t.visitDateDay||t.visitDate;break;case h.DateMillisecond:r=t.visitDateMillisecond||t.visitDate;break;case h.Timestamp:r=t.visitTimestamp;break;case h.TimestampSecond:r=t.visitTimestampSecond||t.visitTimestamp;break;case h.TimestampMillisecond:r=t.visitTimestampMillisecond||t.visitTimestamp;break;case h.TimestampMicrosecond:r=t.visitTimestampMicrosecond||t.visitTimestamp;break;case h.TimestampNanosecond:r=t.visitTimestampNanosecond||t.visitTimestamp;break;case h.Time:r=t.visitTime;break;case h.TimeSecond:r=t.visitTimeSecond||t.visitTime;break;case h.TimeMillisecond:r=t.visitTimeMillisecond||t.visitTime;break;case h.TimeMicrosecond:r=t.visitTimeMicrosecond||t.visitTime;break;case h.TimeNanosecond:r=t.visitTimeNanosecond||t.visitTime;break;case h.Decimal:r=t.visitDecimal;break;case h.List:r=t.visitList;break;case h.Struct:r=t.visitStruct;break;case h.Union:r=t.visitUnion;break;case h.DenseUnion:r=t.visitDenseUnion||t.visitUnion;break;case h.SparseUnion:r=t.visitSparseUnion||t.visitUnion;break;case h.Dictionary:r=t.visitDictionary;break;case h.Interval:r=t.visitInterval;break;case h.IntervalDayTime:r=t.visitIntervalDayTime||t.visitInterval;break;case h.IntervalYearMonth:r=t.visitIntervalYearMonth||t.visitInterval;break;case h.Duration:r=t.visitDuration;break;case h.DurationSecond:r=t.visitDurationSecond||t.visitDuration;break;case h.DurationMillisecond:r=t.visitDurationMillisecond||t.visitDuration;break;case h.DurationMicrosecond:r=t.visitDurationMicrosecond||t.visitDuration;break;case h.DurationNanosecond:r=t.visitDurationNanosecond||t.visitDuration;break;case h.FixedSizeList:r=t.visitFixedSizeList;break;case h.Map:r=t.visitMap}if("function"==typeof r)return r;if(!i)return()=>null;throw new Error(`Unrecognized type '${h[e]}'`)}function Go(t){switch(t.typeId){case h.Null:return h.Null;case h.Int:{const{bitWidth:e,isSigned:i}=t;switch(e){case 8:return i?h.Int8:h.Uint8;case 16:return i?h.Int16:h.Uint16;case 32:return i?h.Int32:h.Uint32;case 64:return i?h.Int64:h.Uint64}return h.Int}case h.Float:switch(t.precision){case xt.HALF:return h.Float16;case xt.SINGLE:return h.Float32;case xt.DOUBLE:return h.Float64}return h.Float;case h.Binary:return h.Binary;case h.LargeBinary:return h.LargeBinary;case h.Utf8:return h.Utf8;case h.LargeUtf8:return h.LargeUtf8;case h.Bool:return h.Bool;case h.Decimal:return h.Decimal;case h.Time:switch(t.unit){case x.SECOND:return h.TimeSecond;case x.MILLISECOND:return h.TimeMillisecond;case x.MICROSECOND:return h.TimeMicrosecond;case x.NANOSECOND:return h.TimeNanosecond}return h.Time;case h.Timestamp:switch(t.unit){case x.SECOND:return h.TimestampSecond;case x.MILLISECOND:return h.TimestampMillisecond;case x.MICROSECOND:return h.TimestampMicrosecond;case x.NANOSECOND:return h.TimestampNanosecond}return h.Timestamp;case h.Date:switch(t.unit){case le.DAY:return h.DateDay;case le.MILLISECOND:return h.DateMillisecond}return h.Date;case h.Interval:switch(t.unit){case De.DAY_TIME:return h.IntervalDayTime;case De.YEAR_MONTH:return h.IntervalYearMonth}return h.Interval;case h.Duration:switch(t.unit){case x.SECOND:return h.DurationSecond;case x.MILLISECOND:return h.DurationMillisecond;case x.MICROSECOND:return h.DurationMicrosecond;case x.NANOSECOND:return h.DurationNanosecond}return h.Duration;case h.Map:return h.Map;case h.List:return h.List;case h.Struct:return h.Struct;case h.Union:switch(t.mode){case Pt.Dense:return h.DenseUnion;case Pt.Sparse:return h.SparseUnion}return h.Union;case h.FixedSizeBinary:return h.FixedSizeBinary;case h.FixedSizeList:return h.FixedSizeList;case h.Dictionary:return h.Dictionary}throw new Error(`Unrecognized type '${h[t.typeId]}'`)}J.prototype.visitInt8=null,J.prototype.visitInt16=null,J.prototype.visitInt32=null,J.prototype.visitInt64=null,J.prototype.visitUint8=null,J.prototype.visitUint16=null,J.prototype.visitUint32=null,J.prototype.visitUint64=null,J.prototype.visitFloat16=null,J.prototype.visitFloat32=null,J.prototype.visitFloat64=null,J.prototype.visitDateDay=null,J.prototype.visitDateMillisecond=null,J.prototype.visitTimestampSecond=null,J.prototype.visitTimestampMillisecond=null,J.prototype.visitTimestampMicrosecond=null,J.prototype.visitTimestampNanosecond=null,J.prototype.visitTimeSecond=null,J.prototype.visitTimeMillisecond=null,J.prototype.visitTimeMicrosecond=null,J.prototype.visitTimeNanosecond=null,J.prototype.visitDenseUnion=null,J.prototype.visitSparseUnion=null,J.prototype.visitIntervalDayTime=null,J.prototype.visitIntervalYearMonth=null,J.prototype.visitDuration=null,J.prototype.visitDurationSecond=null,J.prototype.visitDurationMillisecond=null,J.prototype.visitDurationMicrosecond=null,J.prototype.visitDurationNanosecond=null;const nc=new Float64Array(1),An=new Uint32Array(nc.buffer);function ic(t){const e=(31744&t)>>10,i=(1023&t)/1024,r=Math.pow(-1,(32768&t)>>15);switch(e){case 31:return r*(i?Number.NaN:1/0);case 0:return r*(i?6103515625e-14*i:0)}return r*Math.pow(2,e-15)*(1+i)}function fu(t){if(t!=t)return 32256;nc[0]=t;const e=(2147483648&An[1])>>16&65535;let i=2146435072&An[1],r=0;return i>=1089470464?An[0]>0?i=31744:(i=(2080374784&i)>>16,r=(1048575&An[1])>>10):i<=1056964608?(r=1048576+(1048575&An[1]),r=1048576+(r<<(i>>20)-998)>>21,i=0):(i=i-1056964608>>10,r=512+(1048575&An[1])>>10),e|i|65535&r}class P extends J{}function V(t){return(e,i,r)=>{if(e.setValid(i,null!=r))return t(e,i,r)}}const pu=(t,e,i)=>{t[e]=Math.floor(i/864e5)},rc=(t,e,i,r)=>{if(i+1<e.length){const n=pt(e[i]),s=pt(e[i+1]);t.set(r.subarray(0,s-n),n)}},yu=({offset:t,values:e},i,r)=>{const n=t+i;r?e[n>>3]|=1<<n%8:e[n>>3]&=~(1<<n%8)},Ve=({values:t},e,i)=>{t[e]=i},Ws=({values:t},e,i)=>{t[e]=i},sc=({values:t},e,i)=>{t[e]=fu(i)},mu=(t,e,i)=>{switch(t.type.precision){case xt.HALF:return sc(t,e,i);case xt.SINGLE:case xt.DOUBLE:return Ws(t,e,i)}},oc=({values:t},e,i)=>{pu(t,e,i.valueOf())},ac=({values:t},e,i)=>{t[e]=BigInt(i)},gu=({stride:t,values:e},i,r)=>{e.set(r.subarray(0,t),t*i)},cc=({values:t,valueOffsets:e},i,r)=>rc(t,e,i,r),lc=({values:t,valueOffsets:e},i,r)=>rc(t,e,i,$s(r)),bu=(t,e,i)=>{t.type.unit===le.DAY?oc(t,e,i):ac(t,e,i)},uc=({values:t},e,i)=>{t[e]=BigInt(i/1e3)},hc=({values:t},e,i)=>{t[e]=BigInt(i)},dc=({values:t},e,i)=>{t[e]=BigInt(1e3*i)},fc=({values:t},e,i)=>{t[e]=BigInt(1e6*i)},_u=(t,e,i)=>{switch(t.type.unit){case x.SECOND:return uc(t,e,i);case x.MILLISECOND:return hc(t,e,i);case x.MICROSECOND:return dc(t,e,i);case x.NANOSECOND:return fc(t,e,i)}},pc=({values:t},e,i)=>{t[e]=i},yc=({values:t},e,i)=>{t[e]=i},mc=({values:t},e,i)=>{t[e]=i},gc=({values:t},e,i)=>{t[e]=i},wu=(t,e,i)=>{switch(t.type.unit){case x.SECOND:return pc(t,e,i);case x.MILLISECOND:return yc(t,e,i);case x.MICROSECOND:return mc(t,e,i);case x.NANOSECOND:return gc(t,e,i)}},Iu=({values:t,stride:e},i,r)=>{t.set(r.subarray(0,e),e*i)},vu=(t,e,i)=>{const r=t.children[0],n=t.valueOffsets,s=ue.getVisitFn(r);if(Array.isArray(i))for(let t=-1,o=n[e],a=n[e+1];o<a;)s(r,o++,i[++t]);else for(let t=-1,o=n[e],a=n[e+1];o<a;)s(r,o++,i.get(++t))},Su=(t,e,i)=>{const r=t.children[0],{valueOffsets:n}=t,s=ue.getVisitFn(r);let{[e]:o,[e+1]:a}=n;const l=i instanceof Map?i.entries():Object.entries(i);for(const t of l)if(s(r,o,t),++o>=a)break},Eu=(t,e)=>(i,r,n,s)=>r&&i(r,t,e[s]),Tu=(t,e)=>(i,r,n,s)=>r&&i(r,t,e.get(s)),Au=(t,e)=>(i,r,n,s)=>r&&i(r,t,e.get(n.name)),Fu=(t,e)=>(i,r,n,s)=>r&&i(r,t,e[n.name]),Ou=(t,e,i)=>{const r=t.type.children.map((t=>ue.getVisitFn(t.type))),n=i instanceof Map?Au(e,i):i instanceof ct?Tu(e,i):Array.isArray(i)?Eu(e,i):Fu(e,i);t.type.children.forEach(((e,i)=>n(r[i],t.children[i],e,i)))},Du=(t,e,i)=>{t.type.mode===Pt.Dense?bc(t,e,i):_c(t,e,i)},bc=(t,e,i)=>{const r=t.type.typeIdToChildIndex[t.typeIds[e]],n=t.children[r];ue.visit(n,t.valueOffsets[e],i)},_c=(t,e,i)=>{const r=t.type.typeIdToChildIndex[t.typeIds[e]],n=t.children[r];ue.visit(n,e,i)},Nu=(t,e,i)=>{var r;null===(r=t.dictionary)||void 0===r||r.set(t.values[e],i)},Bu=(t,e,i)=>{t.type.unit===De.DAY_TIME?wc(t,e,i):Ic(t,e,i)},wc=({values:t},e,i)=>{t.set(i.subarray(0,2),2*e)},Ic=({values:t},e,i)=>{t[e]=12*i[0]+i[1]%12},vc=({values:t},e,i)=>{t[e]=i},Sc=({values:t},e,i)=>{t[e]=i},Ec=({values:t},e,i)=>{t[e]=i},Tc=({values:t},e,i)=>{t[e]=i},Ru=(t,e,i)=>{switch(t.type.unit){case x.SECOND:return vc(t,e,i);case x.MILLISECOND:return Sc(t,e,i);case x.MICROSECOND:return Ec(t,e,i);case x.NANOSECOND:return Tc(t,e,i)}},Mu=(t,e,i)=>{const{stride:r}=t,n=t.children[0],s=ue.getVisitFn(n);if(Array.isArray(i))for(let t=-1,o=e*r;++t<r;)s(n,o+t,i[t]);else for(let t=-1,o=e*r;++t<r;)s(n,o+t,i.get(t))};P.prototype.visitBool=V(yu),P.prototype.visitInt=V(Ve),P.prototype.visitInt8=V(Ve),P.prototype.visitInt16=V(Ve),P.prototype.visitInt32=V(Ve),P.prototype.visitInt64=V(Ve),P.prototype.visitUint8=V(Ve),P.prototype.visitUint16=V(Ve),P.prototype.visitUint32=V(Ve),P.prototype.visitUint64=V(Ve),P.prototype.visitFloat=V(mu),P.prototype.visitFloat16=V(sc),P.prototype.visitFloat32=V(Ws),P.prototype.visitFloat64=V(Ws),P.prototype.visitUtf8=V(lc),P.prototype.visitLargeUtf8=V(lc),P.prototype.visitBinary=V(cc),P.prototype.visitLargeBinary=V(cc),P.prototype.visitFixedSizeBinary=V(gu),P.prototype.visitDate=V(bu),P.prototype.visitDateDay=V(oc),P.prototype.visitDateMillisecond=V(ac),P.prototype.visitTimestamp=V(_u),P.prototype.visitTimestampSecond=V(uc),P.prototype.visitTimestampMillisecond=V(hc),P.prototype.visitTimestampMicrosecond=V(dc),P.prototype.visitTimestampNanosecond=V(fc),P.prototype.visitTime=V(wu),P.prototype.visitTimeSecond=V(pc),P.prototype.visitTimeMillisecond=V(yc),P.prototype.visitTimeMicrosecond=V(mc),P.prototype.visitTimeNanosecond=V(gc),P.prototype.visitDecimal=V(Iu),P.prototype.visitList=V(vu),P.prototype.visitStruct=V(Ou),P.prototype.visitUnion=V(Du),P.prototype.visitDenseUnion=V(bc),P.prototype.visitSparseUnion=V(_c),P.prototype.visitDictionary=V(Nu),P.prototype.visitInterval=V(Bu),P.prototype.visitIntervalDayTime=V(wc),P.prototype.visitIntervalYearMonth=V(Ic),P.prototype.visitDuration=V(Ru),P.prototype.visitDurationSecond=V(vc),P.prototype.visitDurationMillisecond=V(Sc),P.prototype.visitDurationMicrosecond=V(Ec),P.prototype.visitDurationNanosecond=V(Tc),P.prototype.visitFixedSizeList=V(Mu),P.prototype.visitMap=V(Su);const ue=new P,pe=Symbol.for("parent"),jn=Symbol.for("rowIndex");class Ys{constructor(t,e){return this[pe]=t,this[jn]=e,new Proxy(this,new Lu)}toArray(){return Object.values(this.toJSON())}toJSON(){const t=this[jn],e=this[pe],i=e.type.children,r={};for(let n=-1,s=i.length;++n<s;)r[i[n].name]=Kt.visit(e.children[n],t);return r}toString(){return`{${[...this].map((([t,e])=>`${vi(t)}: ${vi(e)}`)).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}[Symbol.iterator](){return new Cu(this[pe],this[jn])}}class Cu{constructor(t,e){this.childIndex=0,this.children=t.children,this.rowIndex=e,this.childFields=t.type.children,this.numChildren=this.childFields.length}[Symbol.iterator](){return this}next(){const t=this.childIndex;return t<this.numChildren?(this.childIndex=t+1,{done:!1,value:[this.childFields[t].name,Kt.visit(this.children[t],this.rowIndex)]}):{done:!0,value:null}}}Object.defineProperties(Ys.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[pe]:{writable:!0,enumerable:!1,configurable:!1,value:null},[jn]:{writable:!0,enumerable:!1,configurable:!1,value:-1}});class Lu{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(t){return t[pe].type.children.map((t=>t.name))}has(t,e){return-1!==t[pe].type.children.findIndex((t=>t.name===e))}getOwnPropertyDescriptor(t,e){if(-1!==t[pe].type.children.findIndex((t=>t.name===e)))return{writable:!0,enumerable:!0,configurable:!0}}get(t,e){if(Reflect.has(t,e))return t[e];const i=t[pe].type.children.findIndex((t=>t.name===e));if(-1!==i){const r=Kt.visit(t[pe].children[i],t[jn]);return Reflect.set(t,e,r),r}}set(t,e,i){const r=t[pe].type.children.findIndex((t=>t.name===e));return-1!==r?(ue.visit(t[pe].children[r],t[jn],i),Reflect.set(t,e,i)):!(!Reflect.has(t,e)&&"symbol"!=typeof e)&&Reflect.set(t,e,i)}}class M extends J{}function k(t){return(e,i)=>e.getValid(i)?t(e,i):null}const Uu=(t,e)=>864e5*t[e],xu=(t,e)=>null,Ac=(t,e,i)=>{if(i+1>=e.length)return null;const r=pt(e[i]),n=pt(e[i+1]);return t.subarray(r,n)},Pu=({offset:t,values:e},i)=>{const r=t+i;return!!(e[r>>3]&1<<r%8)},Fc=({values:t},e)=>Uu(t,e),Oc=({values:t},e)=>pt(t[e]),cn=({stride:t,values:e},i)=>e[t*i],ku=({stride:t,values:e},i)=>ic(e[t*i]),Dc=({values:t},e)=>t[e],$u=({stride:t,values:e},i)=>e.subarray(t*i,t*(i+1)),Nc=({values:t,valueOffsets:e},i)=>Ac(t,e,i),Bc=({values:t,valueOffsets:e},i)=>{const r=Ac(t,e,i);return null!==r?Is(r):null},ju=({values:t},e)=>t[e],Vu=({type:t,values:e},i)=>t.precision!==xt.HALF?e[i]:ic(e[i]),zu=(t,e)=>t.type.unit===le.DAY?Fc(t,e):Oc(t,e),Rc=({values:t},e)=>1e3*pt(t[e]),Mc=({values:t},e)=>pt(t[e]),Cc=({values:t},e)=>Ra(t[e],BigInt(1e3)),Lc=({values:t},e)=>Ra(t[e],BigInt(1e6)),Wu=(t,e)=>{switch(t.type.unit){case x.SECOND:return Rc(t,e);case x.MILLISECOND:return Mc(t,e);case x.MICROSECOND:return Cc(t,e);case x.NANOSECOND:return Lc(t,e)}},Uc=({values:t},e)=>t[e],xc=({values:t},e)=>t[e],Pc=({values:t},e)=>t[e],kc=({values:t},e)=>t[e],Yu=(t,e)=>{switch(t.type.unit){case x.SECOND:return Uc(t,e);case x.MILLISECOND:return xc(t,e);case x.MICROSECOND:return Pc(t,e);case x.NANOSECOND:return kc(t,e)}},Gu=({values:t,stride:e},i)=>zs.decimal(t.subarray(e*i,e*(i+1))),qu=(t,e)=>{const{valueOffsets:i,stride:r,children:n}=t,{[e*r]:s,[e*r+1]:o}=i,a=n[0].slice(s,o-s);return new ct([a])},Hu=(t,e)=>{const{valueOffsets:i,children:r}=t,{[e]:n,[e+1]:s}=i,o=r[0];return new Gs(o.slice(n,s-n))},Qu=(t,e)=>new Ys(t,e),Ju=(t,e)=>t.type.mode===Pt.Dense?$c(t,e):jc(t,e),$c=(t,e)=>{const i=t.type.typeIdToChildIndex[t.typeIds[e]],r=t.children[i];return Kt.visit(r,t.valueOffsets[e])},jc=(t,e)=>{const i=t.type.typeIdToChildIndex[t.typeIds[e]],r=t.children[i];return Kt.visit(r,e)},Ku=(t,e)=>{var i;return null===(i=t.dictionary)||void 0===i?void 0:i.get(t.values[e])},Zu=(t,e)=>t.type.unit===De.DAY_TIME?Vc(t,e):zc(t,e),Vc=({values:t},e)=>t.subarray(2*e,2*(e+1)),zc=({values:t},e)=>{const i=t[e],r=new Int32Array(2);return r[0]=Math.trunc(i/12),r[1]=Math.trunc(i%12),r},Wc=({values:t},e)=>t[e],Yc=({values:t},e)=>t[e],Gc=({values:t},e)=>t[e],qc=({values:t},e)=>t[e],Xu=(t,e)=>{switch(t.type.unit){case x.SECOND:return Wc(t,e);case x.MILLISECOND:return Yc(t,e);case x.MICROSECOND:return Gc(t,e);case x.NANOSECOND:return qc(t,e)}},th=(t,e)=>{const{stride:i,children:r}=t,n=r[0].slice(e*i,i);return new ct([n])};M.prototype.visitNull=k(xu),M.prototype.visitBool=k(Pu),M.prototype.visitInt=k(ju),M.prototype.visitInt8=k(cn),M.prototype.visitInt16=k(cn),M.prototype.visitInt32=k(cn),M.prototype.visitInt64=k(Dc),M.prototype.visitUint8=k(cn),M.prototype.visitUint16=k(cn),M.prototype.visitUint32=k(cn),M.prototype.visitUint64=k(Dc),M.prototype.visitFloat=k(Vu),M.prototype.visitFloat16=k(ku),M.prototype.visitFloat32=k(cn),M.prototype.visitFloat64=k(cn),M.prototype.visitUtf8=k(Bc),M.prototype.visitLargeUtf8=k(Bc),M.prototype.visitBinary=k(Nc),M.prototype.visitLargeBinary=k(Nc),M.prototype.visitFixedSizeBinary=k($u),M.prototype.visitDate=k(zu),M.prototype.visitDateDay=k(Fc),M.prototype.visitDateMillisecond=k(Oc),M.prototype.visitTimestamp=k(Wu),M.prototype.visitTimestampSecond=k(Rc),M.prototype.visitTimestampMillisecond=k(Mc),M.prototype.visitTimestampMicrosecond=k(Cc),M.prototype.visitTimestampNanosecond=k(Lc),M.prototype.visitTime=k(Yu),M.prototype.visitTimeSecond=k(Uc),M.prototype.visitTimeMillisecond=k(xc),M.prototype.visitTimeMicrosecond=k(Pc),M.prototype.visitTimeNanosecond=k(kc),M.prototype.visitDecimal=k(Gu),M.prototype.visitList=k(qu),M.prototype.visitStruct=k(Qu),M.prototype.visitUnion=k(Ju),M.prototype.visitDenseUnion=k($c),M.prototype.visitSparseUnion=k(jc),M.prototype.visitDictionary=k(Ku),M.prototype.visitInterval=k(Zu),M.prototype.visitIntervalDayTime=k(Vc),M.prototype.visitIntervalYearMonth=k(zc),M.prototype.visitDuration=k(Xu),M.prototype.visitDurationSecond=k(Wc),M.prototype.visitDurationMillisecond=k(Yc),M.prototype.visitDurationMicrosecond=k(Gc),M.prototype.visitDurationNanosecond=k(qc),M.prototype.visitFixedSizeList=k(th),M.prototype.visitMap=k(Hu);const Kt=new M,Ln=Symbol.for("keys"),Vn=Symbol.for("vals"),Un=Symbol.for("kKeysAsStrings"),Fs=Symbol.for("_kKeysAsStrings");class Gs{constructor(t){return this[Ln]=new ct([t.children[0]]).memoize(),this[Vn]=t.children[1],new Proxy(this,new nh)}get[Un](){return this[Fs]||(this[Fs]=Array.from(this[Ln].toArray(),String))}[Symbol.iterator](){return new eh(this[Ln],this[Vn])}get size(){return this[Ln].length}toArray(){return Object.values(this.toJSON())}toJSON(){const t=this[Ln],e=this[Vn],i={};for(let r=-1,n=t.length;++r<n;)i[t.get(r)]=Kt.visit(e,r);return i}toString(){return`{${[...this].map((([t,e])=>`${vi(t)}: ${vi(e)}`)).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}}class eh{constructor(t,e){this.keys=t,this.vals=e,this.keyIndex=0,this.numKeys=t.length}[Symbol.iterator](){return this}next(){const t=this.keyIndex;return t===this.numKeys?{done:!0,value:null}:(this.keyIndex++,{done:!1,value:[this.keys.get(t),Kt.visit(this.vals,t)]})}}class nh{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(t){return t[Un]}has(t,e){return t[Un].includes(e)}getOwnPropertyDescriptor(t,e){if(-1!==t[Un].indexOf(e))return{writable:!0,enumerable:!0,configurable:!0}}get(t,e){if(Reflect.has(t,e))return t[e];const i=t[Un].indexOf(e);if(-1!==i){const r=Kt.visit(Reflect.get(t,Vn),i);return Reflect.set(t,e,r),r}}set(t,e,i){const r=t[Un].indexOf(e);return-1!==r?(ue.visit(Reflect.get(t,Vn),r,i),Reflect.set(t,e,i)):!!Reflect.has(t,e)&&Reflect.set(t,e,i)}}let qo;function Hc(t,e,i,r){const{length:n=0}=t;let s="number"!=typeof e?0:e,o="number"!=typeof i?n:i;return s<0&&(s=(s%n+n)%n),o<0&&(o=(o%n+n)%n),o<s&&(qo=s,s=o,o=qo),o>n&&(o=n),r?r(t,s,o):[s,o]}Object.defineProperties(Gs.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[Ln]:{writable:!0,enumerable:!1,configurable:!1,value:null},[Vn]:{writable:!0,enumerable:!1,configurable:!1,value:null},[Fs]:{writable:!0,enumerable:!1,configurable:!1,value:null}});const qs=(t,e)=>t<0?e+t:t,Ho=t=>t!=t;function ni(t){if("object"!=typeof t||null===t)return Ho(t)?Ho:e=>e===t;if(t instanceof Date){const e=t.valueOf();return t=>t instanceof Date&&t.valueOf()===e}return ArrayBuffer.isView(t)?e=>!!e&&tu(t,e):t instanceof Map?rh(t):Array.isArray(t)?ih(t):t instanceof ct?sh(t):oh(t,!0)}function ih(t){const e=[];for(let i=-1,r=t.length;++i<r;)e[i]=ni(t[i]);return Zr(e)}function rh(t){let e=-1;const i=[];for(const r of t.values())i[++e]=ni(r);return Zr(i)}function sh(t){const e=[];for(let i=-1,r=t.length;++i<r;)e[i]=ni(t.get(i));return Zr(e)}function oh(t,e=!1){const i=Object.keys(t);if(!e&&0===i.length)return()=>!1;const r=[];for(let e=-1,n=i.length;++e<n;)r[e]=ni(t[i[e]]);return Zr(r,i)}function Zr(t,e){return i=>{if(!i||"object"!=typeof i)return!1;switch(i.constructor){case Array:return ah(t,i);case Map:return Qo(t,i,i.keys());case Gs:case Ys:case Object:case void 0:return Qo(t,i,e||Object.keys(i))}return i instanceof ct&&ch(t,i)}}function ah(t,e){const i=t.length;if(e.length!==i)return!1;for(let r=-1;++r<i;)if(!t[r](e[r]))return!1;return!0}function ch(t,e){const i=t.length;if(e.length!==i)return!1;for(let r=-1;++r<i;)if(!t[r](e.get(r)))return!1;return!0}function Qo(t,e,i){const r=i[Symbol.iterator](),n=e instanceof Map?e.keys():Object.keys(e)[Symbol.iterator](),s=e instanceof Map?e.values():Object.values(e)[Symbol.iterator]();let o=0;const a=t.length;let l=s.next(),c=r.next(),u=n.next();for(;o<a&&!c.done&&!u.done&&!l.done&&c.value===u.value&&t[o](l.value);++o,c=r.next(),u=n.next(),l=s.next());return!!(o===a&&c.done&&u.done&&l.done)||(r.return&&r.return(),n.return&&n.return(),s.return&&s.return(),!1)}function Qc(t,e,i,r){return!!(i&1<<r)}function lh(t,e,i,r){return(i&1<<r)>>r}function Br(t,e,i){const r=i.byteLength+7&-8;if(t>0||i.byteLength<r){const n=new Uint8Array(r);return n.set(t%8==0?i.subarray(t>>3):Rr(new Hs(i,t,e,null,Qc)).subarray(0,r)),n}return i}function Rr(t){const e=[];let i=0,r=0,n=0;for(const s of t)s&&(n|=1<<r),8===++r&&(e[i++]=n,n=r=0);(0===i||r>0)&&(e[i++]=n);const s=new Uint8Array(e.length+7&-8);return s.set(e),s}class Hs{constructor(t,e,i,r,n){this.bytes=t,this.length=i,this.context=r,this.get=n,this.bit=e%8,this.byteIndex=e>>3,this.byte=t[this.byteIndex++],this.index=0}next(){return this.index<this.length?(8===this.bit&&(this.bit=0,this.byte=this.bytes[this.byteIndex++]),{value:this.get(this.context,this.index++,this.byte,this.bit++)}):{done:!0,value:null}}[Symbol.iterator](){return this}}function Os(t,e,i){if(i-e<=0)return 0;if(i-e<8){let r=0;for(const n of new Hs(t,e,i-e,t,lh))r+=n;return r}const r=i>>3<<3,n=e+(e%8==0?0:8-e%8);return Os(t,e,n)+Os(t,r,i)+uh(t,n>>3,r-n>>3)}function uh(t,e,i){let r=0,n=Math.trunc(e);const s=new DataView(t.buffer,t.byteOffset,t.byteLength),o=void 0===i?t.byteLength:n+i;for(;o-n>=4;)r+=cs(s.getUint32(n)),n+=4;for(;o-n>=2;)r+=cs(s.getUint16(n)),n+=2;for(;o-n>=1;)r+=cs(s.getUint8(n)),n+=1;return r}function cs(t){let e=Math.trunc(t);return e-=e>>>1&1431655765,e=(858993459&e)+(e>>>2&858993459),16843009*(e+(e>>>4)&252645135)>>>24}const hh=-1;class ht{get typeId(){return this.type.typeId}get ArrayType(){return this.type.ArrayType}get buffers(){return[this.valueOffsets,this.values,this.nullBitmap,this.typeIds]}get nullable(){if(0!==this._nullCount){const{type:t}=this;return D.isSparseUnion(t)||D.isDenseUnion(t)?this.children.some((t=>t.nullable)):this.nullBitmap&&this.nullBitmap.byteLength>0}return!0}get byteLength(){let t=0;const{valueOffsets:e,values:i,nullBitmap:r,typeIds:n}=this;return e&&(t+=e.byteLength),i&&(t+=i.byteLength),r&&(t+=r.byteLength),n&&(t+=n.byteLength),this.children.reduce(((t,e)=>t+e.byteLength),t)}get nullCount(){if(D.isUnion(this.type))return this.children.reduce(((t,e)=>t+e.nullCount),0);let t,e=this._nullCount;return e<=-1&&(t=this.nullBitmap)&&(this._nullCount=e=0===t.length?0:this.length-Os(t,this.offset,this.offset+this.length)),e}constructor(t,e,i,r,n,s=[],o){let a;this.type=t,this.children=s,this.dictionary=o,this.offset=Math.floor(Math.max(e||0,0)),this.length=Math.floor(Math.max(i||0,0)),this._nullCount=Math.floor(Math.max(r||0,-1)),n instanceof ht?(this.stride=n.stride,this.values=n.values,this.typeIds=n.typeIds,this.nullBitmap=n.nullBitmap,this.valueOffsets=n.valueOffsets):(this.stride=Ue(t),n&&((a=n[0])&&(this.valueOffsets=a),(a=n[1])&&(this.values=a),(a=n[2])&&(this.nullBitmap=a),(a=n[3])&&(this.typeIds=a)))}getValid(t){const{type:e}=this;if(D.isUnion(e)){const i=e,r=this.children[i.typeIdToChildIndex[this.typeIds[t]]],n=i.mode===Pt.Dense?this.valueOffsets[t]:t;return r.getValid(n)}if(this.nullable&&this.nullCount>0){const e=this.offset+t;return!!(this.nullBitmap[e>>3]&1<<e%8)}return!0}setValid(t,e){let i;const{type:r}=this;if(D.isUnion(r)){const n=r,s=this.children[n.typeIdToChildIndex[this.typeIds[t]]],o=n.mode===Pt.Dense?this.valueOffsets[t]:t;i=s.getValid(o),s.setValid(o,e)}else{let{nullBitmap:r}=this;const{offset:n,length:s}=this,o=n+t,a=1<<o%8,l=o>>3;(!r||r.byteLength<=l)&&(r=new Uint8Array((n+s+63&-64)>>3).fill(255),this.nullCount>0?(r.set(Br(n,s,this.nullBitmap),0),Object.assign(this,{nullBitmap:r})):Object.assign(this,{nullBitmap:r,_nullCount:0}));const c=r[l];i=0!==(c&a),r[l]=e?c|a:c&~a}return i!==!!e&&(this._nullCount=this.nullCount+(e?-1:1)),e}clone(t=this.type,e=this.offset,i=this.length,r=this._nullCount,n=this,s=this.children){return new ht(t,e,i,r,n,s,this.dictionary)}slice(t,e){const{stride:i,typeId:r,children:n}=this,s=+(0===this._nullCount)-1,o=16===r?i:1,a=this._sliceBuffers(t,e,i,r);return this.clone(this.type,this.offset+t,e,s,a,0===n.length||this.valueOffsets?n:this._sliceChildren(n,o*t,o*e))}_changeLengthAndBackfillNullBitmap(t){if(this.typeId===h.Null)return this.clone(this.type,0,t,0);const{length:e,nullCount:i}=this,r=new Uint8Array((t+63&-64)>>3).fill(255,0,e>>3);r[e>>3]=(1<<e-(-8&e))-1,i>0&&r.set(Br(this.offset,e,this.nullBitmap),0);const n=this.buffers;return n[Le.VALIDITY]=r,this.clone(this.type,0,t,i+(t-e),n)}_sliceBuffers(t,e,i,r){let n;const{buffers:s}=this;return(n=s[Le.TYPE])&&(s[Le.TYPE]=n.subarray(t,t+e)),(n=s[Le.OFFSET])&&(s[Le.OFFSET]=n.subarray(t,t+e+1))||(n=s[Le.DATA])&&(s[Le.DATA]=6===r?n:n.subarray(i*t,i*(t+e))),s}_sliceChildren(t,e,i){return t.map((t=>t.slice(e,i)))}}ht.prototype.children=Object.freeze([]);class _i extends J{visit(t){return this.getVisitFn(t.type).call(this,t)}visitNull(t){const{type:e,offset:i=0,length:r=0}=t;return new ht(e,i,r,r)}visitBool(t){const{type:e,offset:i=0}=t,r=rt(t.nullBitmap),n=ft(e.ArrayType,t.data),{length:s=n.length>>3,nullCount:o=(t.nullBitmap?-1:0)}=t;return new ht(e,i,s,o,[void 0,n,r])}visitInt(t){const{type:e,offset:i=0}=t,r=rt(t.nullBitmap),n=ft(e.ArrayType,t.data),{length:s=n.length,nullCount:o=(t.nullBitmap?-1:0)}=t;return new ht(e,i,s,o,[void 0,n,r])}visitFloat(t){const{type:e,offset:i=0}=t,r=rt(t.nullBitmap),n=ft(e.ArrayType,t.data),{length:s=n.length,nullCount:o=(t.nullBitmap?-1:0)}=t;return new ht(e,i,s,o,[void 0,n,r])}visitUtf8(t){const{type:e,offset:i=0}=t,r=rt(t.data),n=rt(t.nullBitmap),s=li(t.valueOffsets),{length:o=s.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new ht(e,i,o,a,[s,r,n])}visitLargeUtf8(t){const{type:e,offset:i=0}=t,r=rt(t.data),n=rt(t.nullBitmap),s=Uo(t.valueOffsets),{length:o=s.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new ht(e,i,o,a,[s,r,n])}visitBinary(t){const{type:e,offset:i=0}=t,r=rt(t.data),n=rt(t.nullBitmap),s=li(t.valueOffsets),{length:o=s.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new ht(e,i,o,a,[s,r,n])}visitLargeBinary(t){const{type:e,offset:i=0}=t,r=rt(t.data),n=rt(t.nullBitmap),s=Uo(t.valueOffsets),{length:o=s.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new ht(e,i,o,a,[s,r,n])}visitFixedSizeBinary(t){const{type:e,offset:i=0}=t,r=rt(t.nullBitmap),n=ft(e.ArrayType,t.data),{length:s=n.length/Ue(e),nullCount:o=(t.nullBitmap?-1:0)}=t;return new ht(e,i,s,o,[void 0,n,r])}visitDate(t){const{type:e,offset:i=0}=t,r=rt(t.nullBitmap),n=ft(e.ArrayType,t.data),{length:s=n.length/Ue(e),nullCount:o=(t.nullBitmap?-1:0)}=t;return new ht(e,i,s,o,[void 0,n,r])}visitTimestamp(t){const{type:e,offset:i=0}=t,r=rt(t.nullBitmap),n=ft(e.ArrayType,t.data),{length:s=n.length/Ue(e),nullCount:o=(t.nullBitmap?-1:0)}=t;return new ht(e,i,s,o,[void 0,n,r])}visitTime(t){const{type:e,offset:i=0}=t,r=rt(t.nullBitmap),n=ft(e.ArrayType,t.data),{length:s=n.length/Ue(e),nullCount:o=(t.nullBitmap?-1:0)}=t;return new ht(e,i,s,o,[void 0,n,r])}visitDecimal(t){const{type:e,offset:i=0}=t,r=rt(t.nullBitmap),n=ft(e.ArrayType,t.data),{length:s=n.length/Ue(e),nullCount:o=(t.nullBitmap?-1:0)}=t;return new ht(e,i,s,o,[void 0,n,r])}visitList(t){const{type:e,offset:i=0,child:r}=t,n=rt(t.nullBitmap),s=li(t.valueOffsets),{length:o=s.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new ht(e,i,o,a,[s,void 0,n],[r])}visitStruct(t){const{type:e,offset:i=0,children:r=[]}=t,n=rt(t.nullBitmap),{length:s=r.reduce(((t,{length:e})=>Math.max(t,e)),0),nullCount:o=(t.nullBitmap?-1:0)}=t;return new ht(e,i,s,o,[void 0,void 0,n],r)}visitUnion(t){const{type:e,offset:i=0,children:r=[]}=t,n=ft(e.ArrayType,t.typeIds),{length:s=n.length,nullCount:o=-1}=t;if(D.isSparseUnion(e))return new ht(e,i,s,o,[void 0,void 0,void 0,n],r);const a=li(t.valueOffsets);return new ht(e,i,s,o,[a,void 0,void 0,n],r)}visitDictionary(t){const{type:e,offset:i=0}=t,r=rt(t.nullBitmap),n=ft(e.indices.ArrayType,t.data),{dictionary:s=new ct([(new _i).visit({type:e.dictionary})])}=t,{length:o=n.length,nullCount:a=(t.nullBitmap?-1:0)}=t;return new ht(e,i,o,a,[void 0,n,r],[],s)}visitInterval(t){const{type:e,offset:i=0}=t,r=rt(t.nullBitmap),n=ft(e.ArrayType,t.data),{length:s=n.length/Ue(e),nullCount:o=(t.nullBitmap?-1:0)}=t;return new ht(e,i,s,o,[void 0,n,r])}visitDuration(t){const{type:e,offset:i=0}=t,r=rt(t.nullBitmap),n=ft(e.ArrayType,t.data),{length:s=n.length,nullCount:o=(t.nullBitmap?-1:0)}=t;return new ht(e,i,s,o,[void 0,n,r])}visitFixedSizeList(t){const{type:e,offset:i=0,child:r=(new _i).visit({type:e.valueType})}=t,n=rt(t.nullBitmap),{length:s=r.length/Ue(e),nullCount:o=(t.nullBitmap?-1:0)}=t;return new ht(e,i,s,o,[void 0,void 0,n],[r])}visitMap(t){const{type:e,offset:i=0,child:r=(new _i).visit({type:e.childType})}=t,n=rt(t.nullBitmap),s=li(t.valueOffsets),{length:o=s.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new ht(e,i,o,a,[s,void 0,n],[r])}}const dh=new _i;function X(t){return dh.visit(t)}class Jo{constructor(t=0,e){this.numChunks=t,this.getChunkIterator=e,this.chunkIndex=0,this.chunkIterator=this.getChunkIterator(0)}next(){for(;this.chunkIndex<this.numChunks;){const t=this.chunkIterator.next();if(!t.done)return t;++this.chunkIndex<this.numChunks&&(this.chunkIterator=this.getChunkIterator(this.chunkIndex))}return{done:!0,value:null}}[Symbol.iterator](){return this}}function fh(t){return t.some((t=>t.nullable))}function Jc(t){return t.reduce(((t,e)=>t+e.nullCount),0)}function Kc(t){return t.reduce(((t,e,i)=>(t[i+1]=t[i]+e.length,t)),new Uint32Array(t.length+1))}function Zc(t,e,i,r){const n=[];for(let s=-1,o=t.length;++s<o;){const o=t[s],a=e[s],{length:l}=o;if(a>=r)break;if(i>=a+l)continue;if(a>=i&&a+l<=r){n.push(o);continue}const c=Math.max(0,i-a),u=Math.min(r-a,l);n.push(o.slice(c,u-c))}return 0===n.length&&n.push(t[0].slice(0,0)),n}function Qs(t,e,i,r){let n=0,s=0,o=e.length-1;do{if(n>=o-1)return i<e[o]?r(t,n,i-e[n]):null;s=n+Math.trunc(.5*(o-n)),i<e[s]?o=s:n=s}while(n<o)}function Js(t,e){return t.getValid(e)}function Mr(t){function e(e,i,r){return t(e[i],r)}return function(t){return Qs(this.data,this._offsets,t,e)}}function Xc(t){let e;function i(i,r,n){return t(i[r],n,e)}return function(t,r){const n=this.data;e=r;const s=Qs(n,this._offsets,t,i);return e=void 0,s}}function tl(t){let e;function i(i,r,n){let s=n,o=0,a=0;for(let n=r-1,l=i.length;++n<l;){const r=i[n];if(~(o=t(r,e,s)))return a+o;s=0,a+=r.length}return-1}return function(t,r){e=t;const n=this.data,s="number"!=typeof r?i(n,0,0):Qs(n,this._offsets,r,i);return e=void 0,s}}class C extends J{}function ph(t,e){return null===e&&t.length>0?0:-1}function yh(t,e){const{nullBitmap:i}=t;if(!i||t.nullCount<=0)return-1;let r=0;for(const n of new Hs(i,t.offset+(e||0),t.length,i,Qc)){if(!n)return r;++r}return-1}function z(t,e,i){if(void 0===e)return-1;if(null===e)switch(t.typeId){case h.Union:case h.Dictionary:break;default:return yh(t,i)}const r=Kt.getVisitFn(t),n=ni(e);for(let e=(i||0)-1,s=t.length;++e<s;)if(n(r(t,e)))return e;return-1}function el(t,e,i){const r=Kt.getVisitFn(t),n=ni(e);for(let e=(i||0)-1,s=t.length;++e<s;)if(n(r(t,e)))return e;return-1}C.prototype.visitNull=ph,C.prototype.visitBool=z,C.prototype.visitInt=z,C.prototype.visitInt8=z,C.prototype.visitInt16=z,C.prototype.visitInt32=z,C.prototype.visitInt64=z,C.prototype.visitUint8=z,C.prototype.visitUint16=z,C.prototype.visitUint32=z,C.prototype.visitUint64=z,C.prototype.visitFloat=z,C.prototype.visitFloat16=z,C.prototype.visitFloat32=z,C.prototype.visitFloat64=z,C.prototype.visitUtf8=z,C.prototype.visitLargeUtf8=z,C.prototype.visitBinary=z,C.prototype.visitLargeBinary=z,C.prototype.visitFixedSizeBinary=z,C.prototype.visitDate=z,C.prototype.visitDateDay=z,C.prototype.visitDateMillisecond=z,C.prototype.visitTimestamp=z,C.prototype.visitTimestampSecond=z,C.prototype.visitTimestampMillisecond=z,C.prototype.visitTimestampMicrosecond=z,C.prototype.visitTimestampNanosecond=z,C.prototype.visitTime=z,C.prototype.visitTimeSecond=z,C.prototype.visitTimeMillisecond=z,C.prototype.visitTimeMicrosecond=z,C.prototype.visitTimeNanosecond=z,C.prototype.visitDecimal=z,C.prototype.visitList=z,C.prototype.visitStruct=z,C.prototype.visitUnion=z,C.prototype.visitDenseUnion=el,C.prototype.visitSparseUnion=el,C.prototype.visitDictionary=z,C.prototype.visitInterval=z,C.prototype.visitIntervalDayTime=z,C.prototype.visitIntervalYearMonth=z,C.prototype.visitDuration=z,C.prototype.visitDurationSecond=z,C.prototype.visitDurationMillisecond=z,C.prototype.visitDurationMicrosecond=z,C.prototype.visitDurationNanosecond=z,C.prototype.visitFixedSizeList=z,C.prototype.visitMap=z;const Cr=new C;class L extends J{}function $(t){const{type:e}=t;if(0===t.nullCount&&1===t.stride&&(D.isInt(e)&&64!==e.bitWidth||D.isTime(e)&&64!==e.bitWidth||D.isFloat(e)&&e.precision!==xt.HALF))return new Jo(t.data.length,(e=>{const i=t.data[e];return i.values.subarray(0,i.length)[Symbol.iterator]()}));let i=0;return new Jo(t.data.length,(e=>{const r=t.data[e].length,n=t.slice(i,i+r);return i+=r,new mh(n)}))}class mh{constructor(t){this.vector=t,this.index=0}next(){return this.index<this.vector.length?{value:this.vector.get(this.index++)}:{done:!0,value:null}}[Symbol.iterator](){return this}}L.prototype.visitNull=$,L.prototype.visitBool=$,L.prototype.visitInt=$,L.prototype.visitInt8=$,L.prototype.visitInt16=$,L.prototype.visitInt32=$,L.prototype.visitInt64=$,L.prototype.visitUint8=$,L.prototype.visitUint16=$,L.prototype.visitUint32=$,L.prototype.visitUint64=$,L.prototype.visitFloat=$,L.prototype.visitFloat16=$,L.prototype.visitFloat32=$,L.prototype.visitFloat64=$,L.prototype.visitUtf8=$,L.prototype.visitLargeUtf8=$,L.prototype.visitBinary=$,L.prototype.visitLargeBinary=$,L.prototype.visitFixedSizeBinary=$,L.prototype.visitDate=$,L.prototype.visitDateDay=$,L.prototype.visitDateMillisecond=$,L.prototype.visitTimestamp=$,L.prototype.visitTimestampSecond=$,L.prototype.visitTimestampMillisecond=$,L.prototype.visitTimestampMicrosecond=$,L.prototype.visitTimestampNanosecond=$,L.prototype.visitTime=$,L.prototype.visitTimeSecond=$,L.prototype.visitTimeMillisecond=$,L.prototype.visitTimeMicrosecond=$,L.prototype.visitTimeNanosecond=$,L.prototype.visitDecimal=$,L.prototype.visitList=$,L.prototype.visitStruct=$,L.prototype.visitUnion=$,L.prototype.visitDenseUnion=$,L.prototype.visitSparseUnion=$,L.prototype.visitDictionary=$,L.prototype.visitInterval=$,L.prototype.visitIntervalDayTime=$,L.prototype.visitIntervalYearMonth=$,L.prototype.visitDuration=$,L.prototype.visitDurationSecond=$,L.prototype.visitDurationMillisecond=$,L.prototype.visitDurationMicrosecond=$,L.prototype.visitDurationNanosecond=$,L.prototype.visitFixedSizeList=$,L.prototype.visitMap=$;const Ks=new L;var nl;const il={},rl={};class ct{constructor(t){var e,i,r;const n=t[0]instanceof ct?t.flatMap((t=>t.data)):t;if(0===n.length||n.some((t=>!(t instanceof ht))))throw new TypeError("Vector constructor expects an Array of Data instances.");const s=null===(e=n[0])||void 0===e?void 0:e.type;switch(n.length){case 0:this._offsets=[0];break;case 1:{const{get:t,set:e,indexOf:i}=il[s.typeId],r=n[0];this.isValid=t=>Js(r,t),this.get=e=>t(r,e),this.set=(t,i)=>e(r,t,i),this.indexOf=t=>i(r,t),this._offsets=[0,r.length];break}default:Object.setPrototypeOf(this,rl[s.typeId]),this._offsets=Kc(n)}this.data=n,this.type=s,this.stride=Ue(s),this.numChildren=null!==(r=null===(i=s.children)||void 0===i?void 0:i.length)&&void 0!==r?r:0,this.length=this._offsets.at(-1)}get byteLength(){return this.data.reduce(((t,e)=>t+e.byteLength),0)}get nullable(){return fh(this.data)}get nullCount(){return Jc(this.data)}get ArrayType(){return this.type.ArrayType}get[Symbol.toStringTag](){return`${this.VectorName}<${this.type[Symbol.toStringTag]}>`}get VectorName(){return`${h[this.type.typeId]}Vector`}isValid(t){return!1}get(t){return null}at(t){return this.get(qs(t,this.length))}set(t,e){}indexOf(t,e){return-1}includes(t,e){return this.indexOf(t,e)>-1}[Symbol.iterator](){return Ks.visit(this)}concat(...t){return new ct(this.data.concat(t.flatMap((t=>t.data)).flat(Number.POSITIVE_INFINITY)))}slice(t,e){return new ct(Hc(this,t,e,(({data:t,_offsets:e},i,r)=>Zc(t,e,i,r))))}toJSON(){return[...this]}toArray(){const{type:t,data:e,length:i,stride:r,ArrayType:n}=this;switch(t.typeId){case h.Int:case h.Float:case h.Decimal:case h.Time:case h.Timestamp:switch(e.length){case 0:return new n;case 1:return e[0].values.subarray(0,i*r);default:return e.reduce(((t,{values:e,length:i})=>(t.array.set(e.subarray(0,i*r),t.offset),t.offset+=i*r,t)),{array:new n(i*r),offset:0}).array}}return[...this]}toString(){return`[${[...this].join(",")}]`}getChild(t){var e;return this.getChildAt(null===(e=this.type.children)||void 0===e?void 0:e.findIndex((e=>e.name===t)))}getChildAt(t){return t>-1&&t<this.numChildren?new ct(this.data.map((({children:e})=>e[t]))):null}get isMemoized(){return!!D.isDictionary(this.type)&&this.data[0].dictionary.isMemoized}memoize(){if(D.isDictionary(this.type)){const t=new Lr(this.data[0].dictionary),e=this.data.map((e=>{const i=e.clone();return i.dictionary=t,i}));return new ct(e)}return new Lr(this)}unmemoize(){if(D.isDictionary(this.type)&&this.isMemoized){const t=this.data[0].dictionary.unmemoize(),e=this.data.map((e=>{const i=e.clone();return i.dictionary=t,i}));return new ct(e)}return this}}nl=Symbol.toStringTag,ct[nl]=(t=>{t.type=D.prototype,t.data=[],t.length=0,t.stride=1,t.numChildren=0,t._offsets=new Uint32Array([0]),t[Symbol.isConcatSpreadable]=!0;const e=Object.keys(h).map((t=>h[t])).filter((t=>"number"==typeof t&&t!==h.NONE));for(const i of e){const e=Kt.getVisitFnByTypeId(i),r=ue.getVisitFnByTypeId(i),n=Cr.getVisitFnByTypeId(i);il[i]={get:e,set:r,indexOf:n},rl[i]=Object.create(t,{isValid:{value:Mr(Js)},get:{value:Mr(Kt.getVisitFnByTypeId(i))},set:{value:Xc(ue.getVisitFnByTypeId(i))},indexOf:{value:tl(Cr.getVisitFnByTypeId(i))}})}return"Vector"})(ct.prototype);class Lr extends ct{constructor(t){super(t.data);const e=this.get,i=this.set,r=this.slice,n=new Array(this.length);Object.defineProperty(this,"get",{value(t){const i=n[t];if(void 0!==i)return i;const r=e.call(this,t);return n[t]=r,r}}),Object.defineProperty(this,"set",{value(t,e){i.call(this,t,e),n[t]=e}}),Object.defineProperty(this,"slice",{value:(t,e)=>new Lr(r.call(this,t,e))}),Object.defineProperty(this,"isMemoized",{value:!0}),Object.defineProperty(this,"unmemoize",{value:()=>new ct(this.data)}),Object.defineProperty(this,"memoize",{value:()=>this})}}class Ds{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}offset(){return this.bb.readInt64(this.bb_pos)}metaDataLength(){return this.bb.readInt32(this.bb_pos+8)}bodyLength(){return this.bb.readInt64(this.bb_pos+16)}static sizeOf(){return 24}static createBlock(t,e,i,r){return t.prep(8,24),t.writeInt64(BigInt(r??0)),t.pad(4),t.writeInt32(i),t.writeInt64(BigInt(e??0)),t.offset()}}class Xt{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFooter(t,e){return(e||new Xt).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFooter(t,e){return t.setPosition(t.position()+4),(e||new Xt).__init(t.readInt32(t.position())+t.position(),t)}version(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Dt.V1}schema(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new Ie).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}dictionaries(t,e){const i=this.bb.__offset(this.bb_pos,8);return i?(e||new Ds).__init(this.bb.__vector(this.bb_pos+i)+24*t,this.bb):null}dictionariesLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}recordBatches(t,e){const i=this.bb.__offset(this.bb_pos,10);return i?(e||new Ds).__init(this.bb.__vector(this.bb_pos+i)+24*t,this.bb):null}recordBatchesLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const i=this.bb.__offset(this.bb_pos,12);return i?(e||new Mt).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+i)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startFooter(t){t.startObject(5)}static addVersion(t,e){t.addFieldInt16(0,e,Dt.V1)}static addSchema(t,e){t.addFieldOffset(1,e,0)}static addDictionaries(t,e){t.addFieldOffset(2,e,0)}static startDictionariesVector(t,e){t.startVector(24,e,8)}static addRecordBatches(t,e){t.addFieldOffset(3,e,0)}static startRecordBatchesVector(t,e){t.startVector(24,e,8)}static addCustomMetadata(t,e){t.addFieldOffset(4,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let i=e.length-1;i>=0;i--)t.addOffset(e[i]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endFooter(t){return t.endObject()}static finishFooterBuffer(t,e){t.finish(e)}static finishSizePrefixedFooterBuffer(t,e){t.finish(e,void 0,!0)}}class lt{constructor(t=[],e,i,r=Dt.V5){this.fields=t||[],this.metadata=e||new Map,i||(i=Ns(this.fields)),this.dictionaries=i,this.metadataVersion=r}get[Symbol.toStringTag](){return"Schema"}get names(){return this.fields.map((t=>t.name))}toString(){return`Schema<{ ${this.fields.map(((t,e)=>`${e}: ${t}`)).join(", ")} }>`}select(t){const e=new Set(t),i=this.fields.filter((t=>e.has(t.name)));return new lt(i,this.metadata)}selectAt(t){const e=t.map((t=>this.fields[t])).filter(Boolean);return new lt(e,this.metadata)}assign(...t){const e=t[0]instanceof lt?t[0]:Array.isArray(t[0])?new lt(t[0]):new lt(t),i=[...this.fields],r=Ki(Ki(new Map,this.metadata),e.metadata),n=e.fields.filter((t=>{const e=i.findIndex((e=>e.name===t.name));return!~e||(i[e]=t.clone({metadata:Ki(Ki(new Map,i[e].metadata),t.metadata)}))&&!1})),s=Ns(n,new Map);return new lt([...i,...n],r,new Map([...this.dictionaries,...s]))}}lt.prototype.fields=null,lt.prototype.metadata=null,lt.prototype.dictionaries=null;class _t{static new(...t){let[e,i,r,n]=t;return t[0]&&"object"==typeof t[0]&&(({name:e}=t[0]),void 0===i&&(i=t[0].type),void 0===r&&(r=t[0].nullable),void 0===n&&(n=t[0].metadata)),new _t(`${e}`,i,r,n)}constructor(t,e,i=!1,r){this.name=t,this.type=e,this.nullable=i,this.metadata=r||new Map}get typeId(){return this.type.typeId}get[Symbol.toStringTag](){return"Field"}toString(){return`${this.name}: ${this.type}`}clone(...t){let[e,i,r,n]=t;return t[0]&&"object"==typeof t[0]?({name:e=this.name,type:i=this.type,nullable:r=this.nullable,metadata:n=this.metadata}=t[0]):[e=this.name,i=this.type,r=this.nullable,n=this.metadata]=t,_t.new(e,i,r,n)}}function Ki(t,e){return new Map([...t||new Map,...e||new Map])}function Ns(t,e=new Map){for(let i=-1,r=t.length;++i<r;){const r=t[i].type;if(D.isDictionary(r))if(e.has(r.id)){if(e.get(r.id)!==r.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}else e.set(r.id,r.dictionary);r.children&&r.children.length>0&&Ns(r.children,e)}return e}_t.prototype.type=null,_t.prototype.name=null,_t.prototype.nullable=null,_t.prototype.metadata=null;var gh=Oa,bh=Jn;class Ai{static decode(t){t=new bh(rt(t));const e=Xt.getRootAsFooter(t),i=lt.decode(e.schema(),new Map,e.version());return new _h(i,e)}static encode(t){const e=new gh,i=lt.encode(e,t.schema);Xt.startRecordBatchesVector(e,t.numRecordBatches);for(const i of[...t.recordBatches()].slice().reverse())sn.encode(e,i);const r=e.endVector();Xt.startDictionariesVector(e,t.numDictionaries);for(const i of[...t.dictionaryBatches()].slice().reverse())sn.encode(e,i);const n=e.endVector();return Xt.startFooter(e),Xt.addSchema(e,i),Xt.addVersion(e,Dt.V5),Xt.addRecordBatches(e,r),Xt.addDictionaries(e,n),Xt.finishFooterBuffer(e,Xt.endFooter(e)),e.asUint8Array()}get numRecordBatches(){return this._recordBatches.length}get numDictionaries(){return this._dictionaryBatches.length}constructor(t,e=Dt.V5,i,r){this.schema=t,this.version=e,i&&(this._recordBatches=i),r&&(this._dictionaryBatches=r)}*recordBatches(){for(let t,e=-1,i=this.numRecordBatches;++e<i;)(t=this.getRecordBatch(e))&&(yield t)}*dictionaryBatches(){for(let t,e=-1,i=this.numDictionaries;++e<i;)(t=this.getDictionaryBatch(e))&&(yield t)}getRecordBatch(t){return t>=0&&t<this.numRecordBatches&&this._recordBatches[t]||null}getDictionaryBatch(t){return t>=0&&t<this.numDictionaries&&this._dictionaryBatches[t]||null}}class _h extends Ai{get numRecordBatches(){return this._footer.recordBatchesLength()}get numDictionaries(){return this._footer.dictionariesLength()}constructor(t,e){super(t,e.version()),this._footer=e}getRecordBatch(t){if(t>=0&&t<this.numRecordBatches){const e=this._footer.recordBatches(t);if(e)return sn.decode(e)}return null}getDictionaryBatch(t){if(t>=0&&t<this.numDictionaries){const e=this._footer.dictionaries(t);if(e)return sn.decode(e)}return null}}class sn{static decode(t){return new sn(t.metaDataLength(),t.bodyLength(),t.offset())}static encode(t,e){const{metaDataLength:i}=e,r=BigInt(e.offset),n=BigInt(e.bodyLength);return Ds.createBlock(t,r,i,n)}constructor(t,e,i){this.metaDataLength=t,this.offset=pt(i),this.bodyLength=pt(e)}}const Et=Object.freeze({done:!0,value:void 0});class Ko{constructor(t){this._json=t}get schema(){return this._json.schema}get batches(){return this._json.batches||[]}get dictionaries(){return this._json.dictionaries||[]}}class Zs{tee(){return this._getDOMStream().tee()}pipe(t,e){return this._getNodeStream().pipe(t,e)}pipeTo(t,e){return this._getDOMStream().pipeTo(t,e)}pipeThrough(t,e){return this._getDOMStream().pipeThrough(t,e)}_getDOMStream(){return this._DOMStream||(this._DOMStream=this.toDOMStream())}_getNodeStream(){return this._nodeStream||(this._nodeStream=this.toNodeStream())}}class wh extends Zs{constructor(){super(),this._values=[],this.resolvers=[],this._closedPromise=new Promise((t=>this._closedPromiseResolve=t))}get closed(){return this._closedPromise}cancel(t){return Y(this,void 0,void 0,(function*(){yield this.return(t)}))}write(t){this._ensureOpen()&&(this.resolvers.length<=0?this._values.push(t):this.resolvers.shift().resolve({done:!1,value:t}))}abort(t){this._closedPromiseResolve&&(this.resolvers.length<=0?this._error={error:t}:this.resolvers.shift().reject({done:!0,value:t}))}close(){if(this._closedPromiseResolve){const{resolvers:t}=this;for(;t.length>0;)t.shift().resolve(Et);this._closedPromiseResolve(),this._closedPromiseResolve=void 0}}[Symbol.asyncIterator](){return this}toDOMStream(t){return se.toDOMStream(this._closedPromiseResolve||this._error?this:this._values,t)}toNodeStream(t){return se.toNodeStream(this._closedPromiseResolve||this._error?this:this._values,t)}throw(t){return Y(this,void 0,void 0,(function*(){return yield this.abort(t),Et}))}return(t){return Y(this,void 0,void 0,(function*(){return yield this.close(),Et}))}read(t){return Y(this,void 0,void 0,(function*(){return(yield this.next(t,"read")).value}))}peek(t){return Y(this,void 0,void 0,(function*(){return(yield this.next(t,"peek")).value}))}next(...t){return this._values.length>0?Promise.resolve({done:!1,value:this._values.shift()}):this._error?Promise.reject({done:!0,value:this._error.error}):this._closedPromiseResolve?new Promise(((t,e)=>{this.resolvers.push({resolve:t,reject:e})})):Promise.resolve(Et)}_ensureOpen(){if(this._closedPromiseResolve)return!0;throw new Error("AsyncQueue is closed")}}class or extends wh{write(t){if((t=rt(t)).byteLength>0)return super.write(t)}toString(t=!1){return t?Is(this.toUint8Array(!0)):this.toUint8Array(!1).then(Is)}toUint8Array(t=!1){return t?Oe(this._values)[0]:Y(this,void 0,void 0,(function*(){var t,e,i,r;const n=[];let s=0;try{for(var o,a=!0,l=In(this);!(t=(o=yield l.next()).done);a=!0){r=o.value,a=!1;const t=r;n.push(t),s+=t.byteLength}}catch(t){e={error:t}}finally{try{!a&&!t&&(i=l.return)&&(yield i.call(l))}finally{if(e)throw e.error}}return Oe(n,s)[0]}))}}class Ur{constructor(t){t&&(this.source=new Ih(se.fromIterable(t)))}[Symbol.iterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class Xn{constructor(t){t instanceof Xn?this.source=t.source:t instanceof or?this.source=new un(se.fromAsyncIterable(t)):Ea(t)?this.source=new un(se.fromNodeStream(t)):js(t)?this.source=new un(se.fromDOMStream(t)):Sa(t)?this.source=new un(se.fromDOMStream(t.body)):$i(t)?this.source=new un(se.fromIterable(t)):(Sn(t)||ei(t))&&(this.source=new un(se.fromAsyncIterable(t)))}[Symbol.asyncIterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}get closed(){return this.source.closed}cancel(t){return this.source.cancel(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class Ih{constructor(t){this.source=t}cancel(t){this.return(t)}peek(t){return this.next(t,"peek").value}read(t){return this.next(t,"read").value}next(t,e="read"){return this.source.next({cmd:e,size:t})}throw(t){return Object.create(this.source.throw&&this.source.throw(t)||Et)}return(t){return Object.create(this.source.return&&this.source.return(t)||Et)}}class un{constructor(t){this.source=t,this._closedPromise=new Promise((t=>this._closedPromiseResolve=t))}cancel(t){return Y(this,void 0,void 0,(function*(){yield this.return(t)}))}get closed(){return this._closedPromise}read(t){return Y(this,void 0,void 0,(function*(){return(yield this.next(t,"read")).value}))}peek(t){return Y(this,void 0,void 0,(function*(){return(yield this.next(t,"peek")).value}))}next(t){return Y(this,arguments,void 0,(function*(t,e="read"){return yield this.source.next({cmd:e,size:t})}))}throw(t){return Y(this,void 0,void 0,(function*(){const e=this.source.throw&&(yield this.source.throw(t))||Et;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(e)}))}return(t){return Y(this,void 0,void 0,(function*(){const e=this.source.return&&(yield this.source.return(t))||Et;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(e)}))}}class Zo extends Ur{constructor(t,e){super(),this.position=0,this.buffer=rt(t),this.size=void 0===e?this.buffer.byteLength:e}readInt32(t){const{buffer:e,byteOffset:i}=this.readAt(t,4);return new DataView(e,i).getInt32(0,!0)}seek(t){return this.position=Math.min(t,this.size),t<this.size}read(t){const{buffer:e,size:i,position:r}=this;return e&&r<i?("number"!=typeof t&&(t=Number.POSITIVE_INFINITY),this.position=Math.min(i,r+Math.min(i-r,t)),e.subarray(r,this.position)):null}readAt(t,e){const i=this.buffer,r=Math.min(this.size,t+e);return i?i.subarray(t,r):new Uint8Array(e)}close(){this.buffer&&(this.buffer=null)}throw(t){return this.close(),{done:!0,value:t}}return(t){return this.close(),{done:!0,value:t}}}class xr extends Xn{constructor(t,e){super(),this.position=0,this._handle=t,"number"==typeof e?this.size=e:this._pending=Y(this,void 0,void 0,(function*(){this.size=(yield t.stat()).size,delete this._pending}))}readInt32(t){return Y(this,void 0,void 0,(function*(){const{buffer:e,byteOffset:i}=yield this.readAt(t,4);return new DataView(e,i).getInt32(0,!0)}))}seek(t){return Y(this,void 0,void 0,(function*(){return this._pending&&(yield this._pending),this.position=Math.min(t,this.size),t<this.size}))}read(t){return Y(this,void 0,void 0,(function*(){this._pending&&(yield this._pending);const{_handle:e,size:i,position:r}=this;if(e&&r<i){"number"!=typeof t&&(t=Number.POSITIVE_INFINITY);let n=r,s=0,o=0;const a=Math.min(i,n+Math.min(i-n,t)),l=new Uint8Array(Math.max(0,(this.position=a)-n));for(;(n+=o)<a&&(s+=o)<l.byteLength;)({bytesRead:o}=yield e.read(l,s,l.byteLength-s,n));return l}return null}))}readAt(t,e){return Y(this,void 0,void 0,(function*(){this._pending&&(yield this._pending);const{_handle:i,size:r}=this;if(i&&t+e<r){const n=Math.min(r,t+e),s=new Uint8Array(n-t);return(yield i.read(s,0,e,t)).buffer}return new Uint8Array(e)}))}close(){return Y(this,void 0,void 0,(function*(){const t=this._handle;this._handle=null,t&&(yield t.close())}))}throw(t){return Y(this,void 0,void 0,(function*(){return yield this.close(),{done:!0,value:t}}))}return(t){return Y(this,void 0,void 0,(function*(){return yield this.close(),{done:!0,value:t}}))}}const vh=65536;function Pn(t){return t<0&&(t=4294967295+t+1),`0x${t.toString(16)}`}const ti=8,Xs=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8];class sl{constructor(t){this.buffer=t}high(){return this.buffer[1]}low(){return this.buffer[0]}_times(t){const e=new Uint32Array([this.buffer[1]>>>16,65535&this.buffer[1],this.buffer[0]>>>16,65535&this.buffer[0]]),i=new Uint32Array([t.buffer[1]>>>16,65535&t.buffer[1],t.buffer[0]>>>16,65535&t.buffer[0]]);let r=e[3]*i[3];this.buffer[0]=65535&r;let n=r>>>16;return r=e[2]*i[3],n+=r,r=e[3]*i[2]>>>0,n+=r,this.buffer[0]+=n<<16,this.buffer[1]=n>>>0<r?vh:0,this.buffer[1]+=n>>>16,this.buffer[1]+=e[1]*i[3]+e[2]*i[2]+e[3]*i[1],this.buffer[1]+=e[0]*i[3]+e[1]*i[2]+e[2]*i[1]+e[3]*i[0]<<16,this}_plus(t){const e=this.buffer[0]+t.buffer[0]>>>0;this.buffer[1]+=t.buffer[1],e<this.buffer[0]>>>0&&++this.buffer[1],this.buffer[0]=e}lessThan(t){return this.buffer[1]<t.buffer[1]||this.buffer[1]===t.buffer[1]&&this.buffer[0]<t.buffer[0]}equals(t){return this.buffer[1]===t.buffer[1]&&this.buffer[0]==t.buffer[0]}greaterThan(t){return t.lessThan(this)}hex(){return`${Pn(this.buffer[1])} ${Pn(this.buffer[0])}`}}class dt extends sl{times(t){return this._times(t),this}plus(t){return this._plus(t),this}static from(t,e=new Uint32Array(2)){return dt.fromString("string"==typeof t?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(2)){return dt.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(2)){const i=t.length,r=new dt(e);for(let e=0;e<i;){const n=8<i-e?8:i-e,s=new dt(new Uint32Array([Number.parseInt(t.slice(e,e+n),10),0])),o=new dt(new Uint32Array([Xs[n],0]));r.times(o),r.plus(s),e+=n}return r}static convertArray(t){const e=new Uint32Array(2*t.length);for(let i=-1,r=t.length;++i<r;)dt.from(t[i],new Uint32Array(e.buffer,e.byteOffset+2*i*4,2));return e}static multiply(t,e){return new dt(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new dt(new Uint32Array(t.buffer)).plus(e)}}class qt extends sl{negate(){return this.buffer[0]=1+~this.buffer[0],this.buffer[1]=~this.buffer[1],0==this.buffer[0]&&++this.buffer[1],this}times(t){return this._times(t),this}plus(t){return this._plus(t),this}lessThan(t){const e=0|this.buffer[1],i=0|t.buffer[1];return e<i||e===i&&this.buffer[0]<t.buffer[0]}static from(t,e=new Uint32Array(2)){return qt.fromString("string"==typeof t?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(2)){return qt.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(2)){const i=t.startsWith("-"),r=t.length,n=new qt(e);for(let e=i?1:0;e<r;){const i=8<r-e?8:r-e,s=new qt(new Uint32Array([Number.parseInt(t.slice(e,e+i),10),0])),o=new qt(new Uint32Array([Xs[i],0]));n.times(o),n.plus(s),e+=i}return i?n.negate():n}static convertArray(t){const e=new Uint32Array(2*t.length);for(let i=-1,r=t.length;++i<r;)qt.from(t[i],new Uint32Array(e.buffer,e.byteOffset+2*i*4,2));return e}static multiply(t,e){return new qt(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new qt(new Uint32Array(t.buffer)).plus(e)}}class ve{constructor(t){this.buffer=t}high(){return new qt(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2))}low(){return new qt(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset,2))}negate(){return this.buffer[0]=1+~this.buffer[0],this.buffer[1]=~this.buffer[1],this.buffer[2]=~this.buffer[2],this.buffer[3]=~this.buffer[3],0==this.buffer[0]&&++this.buffer[1],0==this.buffer[1]&&++this.buffer[2],0==this.buffer[2]&&++this.buffer[3],this}times(t){const e=new dt(new Uint32Array([this.buffer[3],0])),i=new dt(new Uint32Array([this.buffer[2],0])),r=new dt(new Uint32Array([this.buffer[1],0])),n=new dt(new Uint32Array([this.buffer[0],0])),s=new dt(new Uint32Array([t.buffer[3],0])),o=new dt(new Uint32Array([t.buffer[2],0])),a=new dt(new Uint32Array([t.buffer[1],0])),l=new dt(new Uint32Array([t.buffer[0],0]));let c=dt.multiply(n,l);this.buffer[0]=c.low();const u=new dt(new Uint32Array([c.high(),0]));return c=dt.multiply(r,l),u.plus(c),c=dt.multiply(n,a),u.plus(c),this.buffer[1]=u.low(),this.buffer[3]=u.lessThan(c)?1:0,this.buffer[2]=u.high(),new dt(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2)).plus(dt.multiply(i,l)).plus(dt.multiply(r,a)).plus(dt.multiply(n,o)),this.buffer[3]+=dt.multiply(e,l).plus(dt.multiply(i,a)).plus(dt.multiply(r,o)).plus(dt.multiply(n,s)).low(),this}plus(t){const e=new Uint32Array(4);return e[3]=this.buffer[3]+t.buffer[3]>>>0,e[2]=this.buffer[2]+t.buffer[2]>>>0,e[1]=this.buffer[1]+t.buffer[1]>>>0,e[0]=this.buffer[0]+t.buffer[0]>>>0,e[0]<this.buffer[0]>>>0&&++e[1],e[1]<this.buffer[1]>>>0&&++e[2],e[2]<this.buffer[2]>>>0&&++e[3],this.buffer[3]=e[3],this.buffer[2]=e[2],this.buffer[1]=e[1],this.buffer[0]=e[0],this}hex(){return`${Pn(this.buffer[3])} ${Pn(this.buffer[2])} ${Pn(this.buffer[1])} ${Pn(this.buffer[0])}`}static multiply(t,e){return new ve(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new ve(new Uint32Array(t.buffer)).plus(e)}static from(t,e=new Uint32Array(4)){return ve.fromString("string"==typeof t?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(4)){return ve.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(4)){const i=t.startsWith("-"),r=t.length,n=new ve(e);for(let e=i?1:0;e<r;){const i=8<r-e?8:r-e,s=new ve(new Uint32Array([Number.parseInt(t.slice(e,e+i),10),0,0,0])),o=new ve(new Uint32Array([Xs[i],0,0,0]));n.times(o),n.plus(s),e+=i}return i?n.negate():n}static convertArray(t){const e=new Uint32Array(4*t.length);for(let i=-1,r=t.length;++i<r;)ve.from(t[i],new Uint32Array(e.buffer,e.byteOffset+16*i,4));return e}}class ol extends J{constructor(t,e,i,r,n=Dt.V5){super(),this.nodesIndex=-1,this.buffersIndex=-1,this.bytes=t,this.nodes=e,this.buffers=i,this.dictionaries=r,this.metadataVersion=n}visit(t){return super.visit(t instanceof _t?t.type:t)}visitNull(t,{length:e}=this.nextFieldNode()){return X({type:t,length:e})}visitBool(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitInt(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitFloat(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitUtf8(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitLargeUtf8(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitBinary(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitLargeBinary(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitFixedSizeBinary(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitDate(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitTimestamp(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitTime(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitDecimal(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitList(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),valueOffsets:this.readOffsets(t),child:this.visit(t.children[0])})}visitStruct(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),children:this.visitMany(t.children)})}visitUnion(t,{length:e,nullCount:i}=this.nextFieldNode()){return this.metadataVersion<Dt.V5&&this.readNullBitmap(t,i),t.mode===Pt.Sparse?this.visitSparseUnion(t,{length:e,nullCount:i}):this.visitDenseUnion(t,{length:e,nullCount:i})}visitDenseUnion(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,typeIds:this.readTypeIds(t),valueOffsets:this.readOffsets(t),children:this.visitMany(t.children)})}visitSparseUnion(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,typeIds:this.readTypeIds(t),children:this.visitMany(t.children)})}visitDictionary(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t.indices),dictionary:this.readDictionary(t)})}visitInterval(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitDuration(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitFixedSizeList(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),child:this.visit(t.children[0])})}visitMap(t,{length:e,nullCount:i}=this.nextFieldNode()){return X({type:t,length:e,nullCount:i,nullBitmap:this.readNullBitmap(t,i),valueOffsets:this.readOffsets(t),child:this.visit(t.children[0])})}nextFieldNode(){return this.nodes[++this.nodesIndex]}nextBufferRange(){return this.buffers[++this.buffersIndex]}readNullBitmap(t,e,i=this.nextBufferRange()){return e>0&&this.readData(t,i)||new Uint8Array(0)}readOffsets(t,e){return this.readData(t,e)}readTypeIds(t,e){return this.readData(t,e)}readData(t,{length:e,offset:i}=this.nextBufferRange()){return this.bytes.subarray(i,i+e)}readDictionary(t){return this.dictionaries.get(t.id)}}class Sh extends ol{constructor(t,e,i,r,n){super(new Uint8Array(0),e,i,r,n),this.sources=t}readNullBitmap(t,e,{offset:i}=this.nextBufferRange()){return e<=0?new Uint8Array(0):Rr(this.sources[i])}readOffsets(t,{offset:e}=this.nextBufferRange()){return ft(Uint8Array,ft(t.OffsetArrayType,this.sources[e]))}readTypeIds(t,{offset:e}=this.nextBufferRange()){return ft(Uint8Array,ft(t.ArrayType,this.sources[e]))}readData(t,{offset:e}=this.nextBufferRange()){const{sources:i}=this;return D.isTimestamp(t)||(D.isInt(t)||D.isTime(t))&&64===t.bitWidth||D.isDuration(t)||D.isDate(t)&&t.unit===le.MILLISECOND?ft(Uint8Array,qt.convertArray(i[e])):D.isDecimal(t)?ft(Uint8Array,ve.convertArray(i[e])):D.isBinary(t)||D.isLargeBinary(t)||D.isFixedSizeBinary(t)?Eh(i[e]):D.isBool(t)?Rr(i[e]):D.isUtf8(t)||D.isLargeUtf8(t)?$s(i[e].join("")):ft(Uint8Array,ft(t.ArrayType,i[e].map((t=>+t))))}}function Eh(t){const e=t.join(""),i=new Uint8Array(e.length/2);for(let t=0;t<e.length;t+=2)i[t>>1]=Number.parseInt(e.slice(t,t+2),16);return i}class U extends J{compareSchemas(t,e){return t===e||e instanceof t.constructor&&this.compareManyFields(t.fields,e.fields)}compareManyFields(t,e){return t===e||Array.isArray(t)&&Array.isArray(e)&&t.length===e.length&&t.every(((t,i)=>this.compareFields(t,e[i])))}compareFields(t,e){return t===e||e instanceof t.constructor&&t.name===e.name&&t.nullable===e.nullable&&this.visit(t.type,e.type)}}function Yt(t,e){return e instanceof t.constructor}function Tn(t,e){return t===e||Yt(t,e)}function ze(t,e){return t===e||Yt(t,e)&&t.bitWidth===e.bitWidth&&t.isSigned===e.isSigned}function Xr(t,e){return t===e||Yt(t,e)&&t.precision===e.precision}function Th(t,e){return t===e||Yt(t,e)&&t.byteWidth===e.byteWidth}function to(t,e){return t===e||Yt(t,e)&&t.unit===e.unit}function ji(t,e){return t===e||Yt(t,e)&&t.unit===e.unit&&t.timezone===e.timezone}function Vi(t,e){return t===e||Yt(t,e)&&t.unit===e.unit&&t.bitWidth===e.bitWidth}function Ah(t,e){return t===e||Yt(t,e)&&t.children.length===e.children.length&&on.compareManyFields(t.children,e.children)}function Fh(t,e){return t===e||Yt(t,e)&&t.children.length===e.children.length&&on.compareManyFields(t.children,e.children)}function eo(t,e){return t===e||Yt(t,e)&&t.mode===e.mode&&t.typeIds.every(((t,i)=>t===e.typeIds[i]))&&on.compareManyFields(t.children,e.children)}function Oh(t,e){return t===e||Yt(t,e)&&t.id===e.id&&t.isOrdered===e.isOrdered&&on.visit(t.indices,e.indices)&&on.visit(t.dictionary,e.dictionary)}function no(t,e){return t===e||Yt(t,e)&&t.unit===e.unit}function zi(t,e){return t===e||Yt(t,e)&&t.unit===e.unit}function Dh(t,e){return t===e||Yt(t,e)&&t.listSize===e.listSize&&t.children.length===e.children.length&&on.compareManyFields(t.children,e.children)}function Nh(t,e){return t===e||Yt(t,e)&&t.keysSorted===e.keysSorted&&t.children.length===e.children.length&&on.compareManyFields(t.children,e.children)}U.prototype.visitNull=Tn,U.prototype.visitBool=Tn,U.prototype.visitInt=ze,U.prototype.visitInt8=ze,U.prototype.visitInt16=ze,U.prototype.visitInt32=ze,U.prototype.visitInt64=ze,U.prototype.visitUint8=ze,U.prototype.visitUint16=ze,U.prototype.visitUint32=ze,U.prototype.visitUint64=ze,U.prototype.visitFloat=Xr,U.prototype.visitFloat16=Xr,U.prototype.visitFloat32=Xr,U.prototype.visitFloat64=Xr,U.prototype.visitUtf8=Tn,U.prototype.visitLargeUtf8=Tn,U.prototype.visitBinary=Tn,U.prototype.visitLargeBinary=Tn,U.prototype.visitFixedSizeBinary=Th,U.prototype.visitDate=to,U.prototype.visitDateDay=to,U.prototype.visitDateMillisecond=to,U.prototype.visitTimestamp=ji,U.prototype.visitTimestampSecond=ji,U.prototype.visitTimestampMillisecond=ji,U.prototype.visitTimestampMicrosecond=ji,U.prototype.visitTimestampNanosecond=ji,U.prototype.visitTime=Vi,U.prototype.visitTimeSecond=Vi,U.prototype.visitTimeMillisecond=Vi,U.prototype.visitTimeMicrosecond=Vi,U.prototype.visitTimeNanosecond=Vi,U.prototype.visitDecimal=Tn,U.prototype.visitList=Ah,U.prototype.visitStruct=Fh,U.prototype.visitUnion=eo,U.prototype.visitDenseUnion=eo,U.prototype.visitSparseUnion=eo,U.prototype.visitDictionary=Oh,U.prototype.visitInterval=no,U.prototype.visitIntervalDayTime=no,U.prototype.visitIntervalYearMonth=no,U.prototype.visitDuration=zi,U.prototype.visitDurationSecond=zi,U.prototype.visitDurationMillisecond=zi,U.prototype.visitDurationMicrosecond=zi,U.prototype.visitDurationNanosecond=zi,U.prototype.visitFixedSizeList=Dh,U.prototype.visitMap=Nh;const on=new U;function Bs(t,e){return on.compareSchemas(t,e)}function ls(t,e){return Bh(t,e.map((t=>t.data.concat())))}function Bh(t,e){const i=[...t.fields],r=[],n={numBatches:e.reduce(((t,e)=>Math.max(t,e.length)),0)};let s=0,o=0,a=-1;const l=e.length;let c,u=[];for(;n.numBatches-- >0;){for(o=Number.POSITIVE_INFINITY,a=-1;++a<l;)u[a]=c=e[a].shift(),o=Math.min(o,c?c.length:o);Number.isFinite(o)&&(u=Rh(i,o,u,e,n),o>0&&(r[s++]=X({type:new zt(i),length:o,nullCount:0,children:u.slice()})))}return[t=t.assign(i),r.map((e=>new Qt(t,e)))]}function Rh(t,e,i,r,n){var s;const o=(e+63&-64)>>3;for(let a=-1,l=r.length;++a<l;){const l=i[a],c=null==l?void 0:l.length;if(c>=e)c===e?i[a]=l:(i[a]=l.slice(0,e),n.numBatches=Math.max(n.numBatches,r[a].unshift(l.slice(e,c-e))));else{const r=t[a];t[a]=r.clone({nullable:!0}),i[a]=null!==(s=null==l?void 0:l._changeLengthAndBackfillNullBitmap(e))&&void 0!==s?s:X({type:r.type,length:e,nullCount:e,nullBitmap:new Uint8Array(o)})}}return i}var al,cl;class Vt{constructor(...t){var e,i;if(0===t.length)return this.batches=[],this.schema=new lt([]),this._offsets=[0],this;let r,n;t[0]instanceof lt&&(r=t.shift()),t.at(-1)instanceof Uint32Array&&(n=t.pop());const s=t=>{if(t){if(t instanceof Qt)return[t];if(t instanceof Vt)return t.batches;if(t instanceof ht){if(t.type instanceof zt)return[new Qt(new lt(t.type.children),t)]}else{if(Array.isArray(t))return t.flatMap((t=>s(t)));if("function"==typeof t[Symbol.iterator])return[...t].flatMap((t=>s(t)));if("object"==typeof t){const e=Object.keys(t),i=e.map((e=>new ct([t[e]]))),n=r??new lt(e.map(((t,e)=>new _t(String(t),i[e].type,i[e].nullable)))),[,s]=ls(n,i);return 0===s.length?[new Qt(t)]:s}}}return[]},o=t.flatMap((t=>s(t)));if(r=null!==(i=r??(null===(e=o[0])||void 0===e?void 0:e.schema))&&void 0!==i?i:new lt([]),!(r instanceof lt))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");for(const t of o){if(!(t instanceof Qt))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");if(!Bs(r,t.schema))throw new TypeError("Table and inner RecordBatch schemas must be equivalent.")}this.schema=r,this.batches=o,this._offsets=n??Kc(this.data)}get data(){return this.batches.map((({data:t})=>t))}get numCols(){return this.schema.fields.length}get numRows(){return this.data.reduce(((t,e)=>t+e.length),0)}get nullCount(){return-1===this._nullCount&&(this._nullCount=Jc(this.data)),this._nullCount}isValid(t){return!1}get(t){return null}at(t){return this.get(qs(t,this.numRows))}set(t,e){}indexOf(t,e){return-1}[Symbol.iterator](){return this.batches.length>0?Ks.visit(new ct(this.data)):new Array(0)[Symbol.iterator]()}toArray(){return[...this]}toString(){return`[\n  ${this.toArray().join(",\n  ")}\n]`}concat(...t){const e=this.schema,i=this.data.concat(t.flatMap((({data:t})=>t)));return new Vt(e,i.map((t=>new Qt(e,t))))}slice(t,e){const i=this.schema;[t,e]=Hc({length:this.numRows},t,e);const r=Zc(this.data,this._offsets,t,e);return new Vt(i,r.map((t=>new Qt(i,t))))}getChild(t){return this.getChildAt(this.schema.fields.findIndex((e=>e.name===t)))}getChildAt(t){if(t>-1&&t<this.schema.fields.length){const e=this.data.map((e=>e.children[t]));if(0===e.length){const{type:i}=this.schema.fields[t],r=X({type:i,length:0,nullCount:0});e.push(r._changeLengthAndBackfillNullBitmap(this.numRows))}return new ct(e)}return null}setChild(t,e){var i;return this.setChildAt(null===(i=this.schema.fields)||void 0===i?void 0:i.findIndex((e=>e.name===t)),e)}setChildAt(t,e){let i=this.schema,r=[...this.batches];if(t>-1&&t<this.numCols){e||(e=new ct([X({type:new rn,length:this.numRows})]));const n=i.fields.slice(),s=n[t].clone({type:e.type}),o=this.schema.fields.map(((t,e)=>this.getChildAt(e)));[n[t],o[t]]=[s,e],[i,r]=ls(i,o)}return new Vt(i,r)}select(t){const e=this.schema.fields.reduce(((t,e,i)=>t.set(e.name,i)),new Map);return this.selectAt(t.map((t=>e.get(t))).filter((t=>t>-1)))}selectAt(t){const e=this.schema.selectAt(t),i=this.batches.map((e=>e.selectAt(t)));return new Vt(e,i)}assign(t){const e=this.schema.fields,[i,r]=t.schema.fields.reduce(((t,i,r)=>{const[n,s]=t,o=e.findIndex((t=>t.name===i.name));return~o?s[o]=r:n.push(r),t}),[[],[]]),n=this.schema.assign(t.schema),s=[...e.map(((t,e)=>[e,r[e]])).map((([e,i])=>void 0===i?this.getChildAt(e):t.getChildAt(i))),...i.map((e=>t.getChildAt(e)))].filter(Boolean);return new Vt(...ls(n,s))}}al=Symbol.toStringTag,Vt[al]=(t=>(t.schema=null,t.batches=[],t._offsets=new Uint32Array([0]),t._nullCount=-1,t[Symbol.isConcatSpreadable]=!0,t.isValid=Mr(Js),t.get=Mr(Kt.getVisitFn(h.Struct)),t.set=Xc(ue.getVisitFn(h.Struct)),t.indexOf=tl(Cr.getVisitFn(h.Struct)),"Table"))(Vt.prototype);let Qt=class t{constructor(...t){switch(t.length){case 2:if([this.schema]=t,!(this.schema instanceof lt))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");if([,this.data=X({nullCount:0,type:new zt(this.schema.fields),children:this.schema.fields.map((t=>X({type:t.type,nullCount:0})))})]=t,!(this.data instanceof ht))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");[this.schema,this.data]=Xo(this.schema,this.data.children);break;case 1:{const[e]=t,{fields:i,children:r,length:n}=Object.keys(e).reduce(((t,i,r)=>(t.children[r]=e[i],t.length=Math.max(t.length,e[i].length),t.fields[r]=_t.new({name:i,type:e[i].type,nullable:!0}),t)),{length:0,fields:new Array,children:new Array}),s=new lt(i),o=X({type:new zt(i),length:n,children:r,nullCount:0});[this.schema,this.data]=Xo(s,o.children,n);break}default:throw new TypeError("RecordBatch constructor expects an Object mapping names to child Data, or a [Schema, Data] pair.")}}get dictionaries(){return this._dictionaries||(this._dictionaries=ll(this.schema.fields,this.data.children))}get numCols(){return this.schema.fields.length}get numRows(){return this.data.length}get nullCount(){return this.data.nullCount}isValid(t){return this.data.getValid(t)}get(t){return Kt.visit(this.data,t)}at(t){return this.get(qs(t,this.numRows))}set(t,e){return ue.visit(this.data,t,e)}indexOf(t,e){return Cr.visit(this.data,t,e)}[Symbol.iterator](){return Ks.visit(new ct([this.data]))}toArray(){return[...this]}concat(...t){return new Vt(this.schema,[this,...t])}slice(e,i){const[r]=new ct([this.data]).slice(e,i).data;return new t(this.schema,r)}getChild(t){var e;return this.getChildAt(null===(e=this.schema.fields)||void 0===e?void 0:e.findIndex((e=>e.name===t)))}getChildAt(t){return t>-1&&t<this.schema.fields.length?new ct([this.data.children[t]]):null}setChild(t,e){var i;return this.setChildAt(null===(i=this.schema.fields)||void 0===i?void 0:i.findIndex((e=>e.name===t)),e)}setChildAt(e,i){let r=this.schema,n=this.data;if(e>-1&&e<this.numCols){i||(i=new ct([X({type:new rn,length:this.numRows})]));const t=r.fields.slice(),s=n.children.slice(),o=t[e].clone({type:i.type});[t[e],s[e]]=[o,i.data[0]],r=new lt(t,new Map(this.schema.metadata)),n=X({type:new zt(t),children:s})}return new t(r,n)}select(e){const i=this.schema.select(e),r=new zt(i.fields),n=[];for(const t of e){const e=this.schema.fields.findIndex((e=>e.name===t));~e&&(n[e]=this.data.children[e])}return new t(i,X({type:r,length:this.numRows,children:n}))}selectAt(e){const i=this.schema.selectAt(e),r=e.map((t=>this.data.children[t])).filter(Boolean),n=X({type:new zt(i.fields),length:this.numRows,children:r});return new t(i,n)}};function Xo(t,e,i=e.reduce(((t,e)=>Math.max(t,e.length)),0)){var r;const n=[...t.fields],s=[...e],o=(i+63&-64)>>3;for(const[a,l]of t.fields.entries()){const t=e[a];(!t||t.length!==i)&&(n[a]=l.clone({nullable:!0}),s[a]=null!==(r=null==t?void 0:t._changeLengthAndBackfillNullBitmap(i))&&void 0!==r?r:X({type:l.type,length:i,nullCount:i,nullBitmap:new Uint8Array(o)}))}return[t.assign(n),X({type:new zt(n),length:i,children:s})]}function ll(t,e,i=new Map){var r,n;if((null!==(r=null==t?void 0:t.length)&&void 0!==r?r:0)>0&&(null==t?void 0:t.length)===(null==e?void 0:e.length))for(let r=-1,s=t.length;++r<s;){const{type:s}=t[r],o=e[r];for(const t of[o,...(null===(n=null==o?void 0:o.dictionary)||void 0===n?void 0:n.data)||[]])ll(s.children,null==t?void 0:t.children,i);if(D.isDictionary(s)){const{id:t}=s;if(i.has(t)){if(i.get(t)!==o.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}else null!=o&&o.dictionary&&i.set(t,o.dictionary)}}return i}cl=Symbol.toStringTag,Qt[cl]=(t=>(t._nullCount=-1,t[Symbol.isConcatSpreadable]=!0,"RecordBatch"))(Qt.prototype);class io extends Qt{constructor(t){const e=t.fields.map((t=>X({type:t.type})));super(t,X({type:new zt(t.fields),nullCount:0,children:e}))}}let Ge=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMessage(e,i){return(i||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsMessage(e,i){return e.setPosition(e.position()+4),(i||new t).__init(e.readInt32(e.position())+e.position(),e)}version(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Dt.V1}headerType(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readUint8(this.bb_pos+t):at.NONE}header(t){const e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__union(t,this.bb_pos+e):null}bodyLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}customMetadata(t,e){const i=this.bb.__offset(this.bb_pos,12);return i?(e||new Mt).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+i)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startMessage(t){t.startObject(5)}static addVersion(t,e){t.addFieldInt16(0,e,Dt.V1)}static addHeaderType(t,e){t.addFieldInt8(1,e,at.NONE)}static addHeader(t,e){t.addFieldOffset(2,e,0)}static addBodyLength(t,e){t.addFieldInt64(3,e,BigInt("0"))}static addCustomMetadata(t,e){t.addFieldOffset(4,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let i=e.length-1;i>=0;i--)t.addOffset(e[i]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endMessage(t){return t.endObject()}static finishMessageBuffer(t,e){t.finish(e)}static finishSizePrefixedMessageBuffer(t,e){t.finish(e,void 0,!0)}static createMessage(e,i,r,n,s,o){return t.startMessage(e),t.addVersion(e,i),t.addHeaderType(e,r),t.addHeader(e,n),t.addBodyLength(e,s),t.addCustomMetadata(e,o),t.endMessage(e)}};class Mh extends J{visit(t,e){return null==t||null==e?void 0:super.visit(t,e)}visitNull(t,e){return Wo.startNull(e),Wo.endNull(e)}visitInt(t,e){return te.startInt(e),te.addBitWidth(e,t.bitWidth),te.addIsSigned(e,t.isSigned),te.endInt(e)}visitFloat(t,e){return Ee.startFloatingPoint(e),Ee.addPrecision(e,t.precision),Ee.endFloatingPoint(e)}visitBinary(t,e){return ko.startBinary(e),ko.endBinary(e)}visitLargeBinary(t,e){return jo.startLargeBinary(e),jo.endLargeBinary(e)}visitBool(t,e){return $o.startBool(e),$o.endBool(e)}visitUtf8(t,e){return Yo.startUtf8(e),Yo.endUtf8(e)}visitLargeUtf8(t,e){return Vo.startLargeUtf8(e),Vo.endLargeUtf8(e)}visitDecimal(t,e){return Dn.startDecimal(e),Dn.addScale(e,t.scale),Dn.addPrecision(e,t.precision),Dn.addBitWidth(e,t.bitWidth),Dn.endDecimal(e)}visitDate(t,e){return tr.startDate(e),tr.addUnit(e,t.unit),tr.endDate(e)}visitTime(t,e){return oe.startTime(e),oe.addUnit(e,t.unit),oe.addBitWidth(e,t.bitWidth),oe.endTime(e)}visitTimestamp(t,e){const i=t.timezone&&e.createString(t.timezone)||void 0;return ae.startTimestamp(e),ae.addUnit(e,t.unit),void 0!==i&&ae.addTimezone(e,i),ae.endTimestamp(e)}visitInterval(t,e){return Te.startInterval(e),Te.addUnit(e,t.unit),Te.endInterval(e)}visitDuration(t,e){return er.startDuration(e),er.addUnit(e,t.unit),er.endDuration(e)}visitList(t,e){return zo.startList(e),zo.endList(e)}visitStruct(t,e){return pn.startStruct_(e),pn.endStruct_(e)}visitUnion(t,e){Ht.startTypeIdsVector(e,t.typeIds.length);const i=Ht.createTypeIdsVector(e,t.typeIds);return Ht.startUnion(e),Ht.addMode(e,t.mode),Ht.addTypeIds(e,i),Ht.endUnion(e)}visitDictionary(t,e){const i=this.visit(t.indices,e);return je.startDictionaryEncoding(e),je.addId(e,BigInt(t.id)),je.addIsOrdered(e,t.isOrdered),void 0!==i&&je.addIndexType(e,i),je.endDictionaryEncoding(e)}visitFixedSizeBinary(t,e){return nr.startFixedSizeBinary(e),nr.addByteWidth(e,t.byteWidth),nr.endFixedSizeBinary(e)}visitFixedSizeList(t,e){return ir.startFixedSizeList(e),ir.addListSize(e,t.listSize),ir.endFixedSizeList(e)}visitMap(t,e){return rr.startMap(e),rr.addKeysSorted(e,t.keysSorted),rr.endMap(e)}}const us=new Mh;function Ch(t,e=new Map){return new lt(Uh(t,e),ar(t.metadata),e)}function ul(t){return new ee(t.count,hl(t.columns),dl(t.columns))}function Lh(t){return new Ne(ul(t.data),t.id,t.isDelta)}function Uh(t,e){return(t.fields||[]).filter(Boolean).map((t=>_t.fromJSON(t,e)))}function ta(t,e){return(t.children||[]).filter(Boolean).map((t=>_t.fromJSON(t,e)))}function hl(t){return(t||[]).reduce(((t,e)=>[...t,new an(e.count,xh(e.VALIDITY)),...hl(e.children)]),[])}function dl(t,e=[]){for(let i=-1,r=(t||[]).length;++i<r;){const r=t[i];r.VALIDITY&&e.push(new Fe(e.length,r.VALIDITY.length)),r.TYPE_ID&&e.push(new Fe(e.length,r.TYPE_ID.length)),r.OFFSET&&e.push(new Fe(e.length,r.OFFSET.length)),r.DATA&&e.push(new Fe(e.length,r.DATA.length)),e=dl(r.children,e)}return e}function xh(t){return(t||[]).reduce(((t,e)=>t+ +(0===e)),0)}function Ph(t,e){let i,r,n,s,o,a;return e&&(s=t.dictionary)?e.has(i=s.id)?(r=(r=s.indexType)?ea(r):new Ti,a=new Zn(e.get(i),r,i,s.isOrdered),n=new _t(t.name,a,t.nullable,ar(t.metadata))):(r=(r=s.indexType)?ea(r):new Ti,e.set(i,o=na(t,ta(t,e))),a=new Zn(o,r,i,s.isOrdered),n=new _t(t.name,a,t.nullable,ar(t.metadata))):(o=na(t,ta(t,e)),n=new _t(t.name,o,t.nullable,ar(t.metadata))),n||null}function ar(t=[]){return new Map(t.map((({key:t,value:e})=>[t,e])))}function ea(t){return new En(t.isSigned,t.bitWidth)}function na(t,e){const i=t.type.name;switch(i){case"NONE":case"null":return new rn;case"binary":return new yr;case"largebinary":return new mr;case"utf8":return new gr;case"largeutf8":return new br;case"bool":return new _r;case"list":return new Ar((e||[])[0]);case"struct":case"struct_":return new zt(e||[])}switch(i){case"int":{const e=t.type;return new En(e.isSigned,e.bitWidth)}case"floatingpoint":{const e=t.type;return new pr(xt[e.precision])}case"decimal":{const e=t.type;return new wr(e.scale,e.precision,e.bitWidth)}case"date":{const e=t.type;return new Ir(le[e.unit])}case"time":{const e=t.type;return new vr(x[e.unit],e.bitWidth)}case"timestamp":{const e=t.type;return new Sr(x[e.unit],e.timezone)}case"interval":{const e=t.type;return new Er(De[e.unit])}case"duration":{const e=t.type;return new Tr(x[e.unit])}case"union":{const i=t.type,[r,...n]=(i.mode+"").toLowerCase(),s=r.toUpperCase()+n.join("");return new Fr(Pt[s],i.typeIds||[],e||[])}case"fixedsizebinary":{const e=t.type;return new Or(e.byteWidth)}case"fixedsizelist":{const i=t.type;return new Dr(i.listSize,(e||[])[0])}case"map":{const i=t.type;return new Nr((e||[])[0],i.keysSorted)}}throw new Error(`Unrecognized type: "${i}"`)}var kh=Oa,$h=Jn;class Wt{static fromJSON(t,e){const i=new Wt(0,Dt.V5,e);return i._createHeader=jh(t,e),i}static decode(t){t=new $h(rt(t));const e=Ge.getRootAsMessage(t),i=e.bodyLength(),r=e.version(),n=e.headerType(),s=new Wt(i,r,n);return s._createHeader=Vh(e,n),s}static encode(t){const e=new kh;let i=-1;return t.isSchema()?i=lt.encode(e,t.header()):t.isRecordBatch()?i=ee.encode(e,t.header()):t.isDictionaryBatch()&&(i=Ne.encode(e,t.header())),Ge.startMessage(e),Ge.addVersion(e,Dt.V5),Ge.addHeader(e,i),Ge.addHeaderType(e,t.headerType),Ge.addBodyLength(e,BigInt(t.bodyLength)),Ge.finishMessageBuffer(e,Ge.endMessage(e)),e.asUint8Array()}static from(t,e=0){if(t instanceof lt)return new Wt(0,Dt.V5,at.Schema,t);if(t instanceof ee)return new Wt(e,Dt.V5,at.RecordBatch,t);if(t instanceof Ne)return new Wt(e,Dt.V5,at.DictionaryBatch,t);throw new Error(`Unrecognized Message header: ${t}`)}get type(){return this.headerType}get version(){return this._version}get headerType(){return this._headerType}get bodyLength(){return this._bodyLength}header(){return this._createHeader()}isSchema(){return this.headerType===at.Schema}isRecordBatch(){return this.headerType===at.RecordBatch}isDictionaryBatch(){return this.headerType===at.DictionaryBatch}constructor(t,e,i,r){this._version=e,this._headerType=i,this.body=new Uint8Array(0),r&&(this._createHeader=()=>r),this._bodyLength=pt(t)}}class ee{get nodes(){return this._nodes}get length(){return this._length}get buffers(){return this._buffers}constructor(t,e,i){this._nodes=e,this._buffers=i,this._length=pt(t)}}class Ne{get id(){return this._id}get data(){return this._data}get isDelta(){return this._isDelta}get length(){return this.data.length}get nodes(){return this.data.nodes}get buffers(){return this.data.buffers}constructor(t,e,i=!1){this._data=t,this._isDelta=i,this._id=pt(e)}}class Fe{constructor(t,e){this.offset=pt(t),this.length=pt(e)}}class an{constructor(t,e){this.length=pt(t),this.nullCount=pt(e)}}function jh(t,e){return()=>{switch(e){case at.Schema:return lt.fromJSON(t);case at.RecordBatch:return ee.fromJSON(t);case at.DictionaryBatch:return Ne.fromJSON(t)}throw new Error(`Unrecognized Message type: { name: ${at[e]}, type: ${e} }`)}}function Vh(t,e){return()=>{switch(e){case at.Schema:return lt.decode(t.header(new Ie),new Map,t.version());case at.RecordBatch:return ee.decode(t.header(new Ce),t.version());case at.DictionaryBatch:return Ne.decode(t.header(new Fn),t.version())}throw new Error(`Unrecognized Message type: { name: ${at[e]}, type: ${e} }`)}}function zh(t,e=new Map,i=Dt.V5){const r=Jh(t,e);return new lt(r,cr(t),e,i)}function Wh(t,e=Dt.V5){if(null!==t.compression())throw new Error("Record batch compression not implemented");return new ee(t.length(),Hh(t),Qh(t,e))}function Yh(t,e=Dt.V5){return new Ne(ee.decode(t.data(),e),t.id(),t.isDelta())}function Gh(t){return new Fe(t.offset(),t.length())}function qh(t){return new an(t.length(),t.nullCount())}function Hh(t){const e=[];for(let i,r=-1,n=-1,s=t.nodesLength();++r<s;)(i=t.nodes(r))&&(e[++n]=an.decode(i));return e}function Qh(t,e){const i=[];for(let r,n=-1,s=-1,o=t.buffersLength();++n<o;)(r=t.buffers(n))&&(e<Dt.V4&&(r.bb_pos+=8*(n+1)),i[++s]=Fe.decode(r));return i}function Jh(t,e){const i=[];for(let r,n=-1,s=-1,o=t.fieldsLength();++n<o;)(r=t.fields(n))&&(i[++s]=_t.decode(r,e));return i}function ia(t,e){const i=[];for(let r,n=-1,s=-1,o=t.childrenLength();++n<o;)(r=t.children(n))&&(i[++s]=_t.decode(r,e));return i}function Kh(t,e){let i,r,n,s,o,a;return e&&(a=t.dictionary())?e.has(i=pt(a.id()))?(s=(s=a.indexType())?ra(s):new Ti,o=new Zn(e.get(i),s,i,a.isOrdered()),r=new _t(t.name(),o,t.nullable(),cr(t))):(s=(s=a.indexType())?ra(s):new Ti,e.set(i,n=sa(t,ia(t,e))),o=new Zn(n,s,i,a.isOrdered()),r=new _t(t.name(),o,t.nullable(),cr(t))):(n=sa(t,ia(t,e)),r=new _t(t.name(),n,t.nullable(),cr(t))),r||null}function cr(t){const e=new Map;if(t)for(let i,r,n=-1,s=Math.trunc(t.customMetadataLength());++n<s;)(i=t.customMetadata(n))&&null!=(r=i.key())&&e.set(r,i.value());return e}function ra(t){return new En(t.isSigned(),t.bitWidth())}function sa(t,e){const i=t.typeType();switch(i){case mt.NONE:case mt.Null:return new rn;case mt.Binary:return new yr;case mt.LargeBinary:return new mr;case mt.Utf8:return new gr;case mt.LargeUtf8:return new br;case mt.Bool:return new _r;case mt.List:return new Ar((e||[])[0]);case mt.Struct_:return new zt(e||[])}switch(i){case mt.Int:{const e=t.type(new te);return new En(e.isSigned(),e.bitWidth())}case mt.FloatingPoint:{const e=t.type(new Ee);return new pr(e.precision())}case mt.Decimal:{const e=t.type(new Dn);return new wr(e.scale(),e.precision(),e.bitWidth())}case mt.Date:{const e=t.type(new tr);return new Ir(e.unit())}case mt.Time:{const e=t.type(new oe);return new vr(e.unit(),e.bitWidth())}case mt.Timestamp:{const e=t.type(new ae);return new Sr(e.unit(),e.timezone())}case mt.Interval:{const e=t.type(new Te);return new Er(e.unit())}case mt.Duration:{const e=t.type(new er);return new Tr(e.unit())}case mt.Union:{const i=t.type(new Ht);return new Fr(i.mode(),i.typeIdsArray()||[],e||[])}case mt.FixedSizeBinary:{const e=t.type(new nr);return new Or(e.byteWidth())}case mt.FixedSizeList:{const i=t.type(new ir);return new Dr(i.listSize(),(e||[])[0])}case mt.Map:{const i=t.type(new rr);return new Nr((e||[])[0],i.keysSorted())}}throw new Error(`Unrecognized type: "${mt[i]}" (${i})`)}function Zh(t,e){const i=e.fields.map((e=>_t.encode(t,e)));Ie.startFieldsVector(t,i.length);const r=Ie.createFieldsVector(t,i),n=e.metadata&&e.metadata.size>0?Ie.createCustomMetadataVector(t,[...e.metadata].map((([e,i])=>{const r=t.createString(`${e}`),n=t.createString(`${i}`);return Mt.startKeyValue(t),Mt.addKey(t,r),Mt.addValue(t,n),Mt.endKeyValue(t)}))):-1;return Ie.startSchema(t),Ie.addFields(t,r),Ie.addEndianness(t,rd?Kn.Little:Kn.Big),-1!==n&&Ie.addCustomMetadata(t,n),Ie.endSchema(t)}function Xh(t,e){let i=-1,r=-1,n=-1;const s=e.type;let o=e.typeId;D.isDictionary(s)?(o=s.dictionary.typeId,n=us.visit(s,t),r=us.visit(s.dictionary,t)):r=us.visit(s,t);const a=(s.children||[]).map((e=>_t.encode(t,e))),l=ne.createChildrenVector(t,a),c=e.metadata&&e.metadata.size>0?ne.createCustomMetadataVector(t,[...e.metadata].map((([e,i])=>{const r=t.createString(`${e}`),n=t.createString(`${i}`);return Mt.startKeyValue(t),Mt.addKey(t,r),Mt.addValue(t,n),Mt.endKeyValue(t)}))):-1;return e.name&&(i=t.createString(e.name)),ne.startField(t),ne.addType(t,r),ne.addTypeType(t,o),ne.addChildren(t,l),ne.addNullable(t,!!e.nullable),-1!==i&&ne.addName(t,i),-1!==n&&ne.addDictionary(t,n),-1!==c&&ne.addCustomMetadata(t,c),ne.endField(t)}function td(t,e){const i=e.nodes||[],r=e.buffers||[];Ce.startNodesVector(t,i.length);for(const e of i.slice().reverse())an.encode(t,e);const n=t.endVector();Ce.startBuffersVector(t,r.length);for(const e of r.slice().reverse())Fe.encode(t,e);const s=t.endVector();return Ce.startRecordBatch(t),Ce.addLength(t,BigInt(e.length)),Ce.addNodes(t,n),Ce.addBuffers(t,s),Ce.endRecordBatch(t)}function ed(t,e){const i=ee.encode(t,e.data);return Fn.startDictionaryBatch(t),Fn.addId(t,BigInt(e.id)),Fn.addIsDelta(t,e.isDelta),Fn.addData(t,i),Fn.endDictionaryBatch(t)}function nd(t,e){return Ba.createFieldNode(t,BigInt(e.length),BigInt(e.nullCount))}function id(t,e){return Na.createBuffer(t,BigInt(e.offset),BigInt(e.length))}_t.encode=Xh,_t.decode=Kh,_t.fromJSON=Ph,lt.encode=Zh,lt.decode=zh,lt.fromJSON=Ch,ee.encode=td,ee.decode=Wh,ee.fromJSON=ul,Ne.encode=ed,Ne.decode=Yh,Ne.fromJSON=Lh,an.encode=nd,an.decode=qh,Fe.encode=id,Fe.decode=Gh;const rd=(()=>{const t=new ArrayBuffer(2);return new DataView(t).setInt16(0,256,!0),256===new Int16Array(t)[0]})(),ro=t=>`Expected ${at[t]} Message in stream, but was null or length 0.`,so=t=>`Header pointer of flatbuffer-encoded ${at[t]} Message is null or length 0.`,fl=(t,e)=>`Expected to read ${t} metadata bytes, but only read ${e}.`,pl=(t,e)=>`Expected to read ${t} bytes for message body, but only read ${e}.`;class yl{constructor(t){this.source=t instanceof Ur?t:new Ur(t)}[Symbol.iterator](){return this}next(){let t;return(t=this.readMetadataLength()).done||-1===t.value&&(t=this.readMetadataLength()).done||(t=this.readMetadata(t.value)).done?Et:t}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}readMessage(t){let e;if((e=this.next()).done)return null;if(null!=t&&e.value.headerType!==t)throw new Error(ro(t));return e.value}readMessageBody(t){if(t<=0)return new Uint8Array(0);const e=rt(this.source.read(t));if(e.byteLength<t)throw new Error(pl(t,e.byteLength));return e.byteOffset%8==0&&e.byteOffset+e.byteLength<=e.buffer.byteLength?e:e.slice()}readSchema(t=!1){const e=at.Schema,i=this.readMessage(e),r=null==i?void 0:i.header();if(t&&!r)throw new Error(so(e));return r}readMetadataLength(){const t=this.source.read(ts),e=t&&new Jn(t),i=(null==e?void 0:e.readInt32(0))||0;return{done:0===i,value:i}}readMetadata(t){const e=this.source.read(t);if(!e)return Et;if(e.byteLength<t)throw new Error(fl(t,e.byteLength));return{done:!1,value:Wt.decode(e)}}}class sd{constructor(t,e){this.source=t instanceof Xn?t:va(t)?new xr(t,e):new Xn(t)}[Symbol.asyncIterator](){return this}next(){return Y(this,void 0,void 0,(function*(){let t;return(t=yield this.readMetadataLength()).done||-1===t.value&&(t=yield this.readMetadataLength()).done||(t=yield this.readMetadata(t.value)).done?Et:t}))}throw(t){return Y(this,void 0,void 0,(function*(){return yield this.source.throw(t)}))}return(t){return Y(this,void 0,void 0,(function*(){return yield this.source.return(t)}))}readMessage(t){return Y(this,void 0,void 0,(function*(){let e;if((e=yield this.next()).done)return null;if(null!=t&&e.value.headerType!==t)throw new Error(ro(t));return e.value}))}readMessageBody(t){return Y(this,void 0,void 0,(function*(){if(t<=0)return new Uint8Array(0);const e=rt(yield this.source.read(t));if(e.byteLength<t)throw new Error(pl(t,e.byteLength));return e.byteOffset%8==0&&e.byteOffset+e.byteLength<=e.buffer.byteLength?e:e.slice()}))}readSchema(){return Y(this,arguments,void 0,(function*(t=!1){const e=at.Schema,i=yield this.readMessage(e),r=null==i?void 0:i.header();if(t&&!r)throw new Error(so(e));return r}))}readMetadataLength(){return Y(this,void 0,void 0,(function*(){const t=yield this.source.read(ts),e=t&&new Jn(t),i=(null==e?void 0:e.readInt32(0))||0;return{done:0===i,value:i}}))}readMetadata(t){return Y(this,void 0,void 0,(function*(){const e=yield this.source.read(t);if(!e)return Et;if(e.byteLength<t)throw new Error(fl(t,e.byteLength));return{done:!1,value:Wt.decode(e)}}))}}class od extends yl{constructor(t){super(new Uint8Array(0)),this._schema=!1,this._body=[],this._batchIndex=0,this._dictionaryIndex=0,this._json=t instanceof Ko?t:new Ko(t)}next(){const{_json:t}=this;if(!this._schema)return this._schema=!0,{done:!1,value:Wt.fromJSON(t.schema,at.Schema)};if(this._dictionaryIndex<t.dictionaries.length){const e=t.dictionaries[this._dictionaryIndex++];return this._body=e.data.columns,{done:!1,value:Wt.fromJSON(e,at.DictionaryBatch)}}if(this._batchIndex<t.batches.length){const e=t.batches[this._batchIndex++];return this._body=e.columns,{done:!1,value:Wt.fromJSON(e,at.RecordBatch)}}return this._body=[],Et}readMessageBody(t){return function t(e){return(e||[]).reduce(((e,i)=>[...e,...i.VALIDITY&&[i.VALIDITY]||[],...i.TYPE_ID&&[i.TYPE_ID]||[],...i.OFFSET&&[i.OFFSET]||[],...i.DATA&&[i.DATA]||[],...t(i.children)]),[])}(this._body)}readMessage(t){let e;if((e=this.next()).done)return null;if(null!=t&&e.value.headerType!==t)throw new Error(ro(t));return e.value}readSchema(){const t=at.Schema,e=this.readMessage(t),i=null==e?void 0:e.header();if(!e||!i)throw new Error(so(t));return i}}const ts=4,Rs="ARROW1",Fi=new Uint8Array(6);for(let t=0;t<6;t+=1)Fi[t]=Rs.codePointAt(t);function oo(t,e=0){for(let i=-1,r=Fi.length;++i<r;)if(Fi[i]!==t[e+i])return!1;return!0}const Wi=Fi.length,ml=Wi+ts,ad=2*Wi+ts;class ce extends Zs{constructor(t){super(),this._impl=t}get closed(){return this._impl.closed}get schema(){return this._impl.schema}get autoDestroy(){return this._impl.autoDestroy}get dictionaries(){return this._impl.dictionaries}get numDictionaries(){return this._impl.numDictionaries}get numRecordBatches(){return this._impl.numRecordBatches}get footer(){return this._impl.isFile()?this._impl.footer:null}isSync(){return this._impl.isSync()}isAsync(){return this._impl.isAsync()}isFile(){return this._impl.isFile()}isStream(){return this._impl.isStream()}next(){return this._impl.next()}throw(t){return this._impl.throw(t)}return(t){return this._impl.return(t)}cancel(){return this._impl.cancel()}reset(t){return this._impl.reset(t),this._DOMStream=void 0,this._nodeStream=void 0,this}open(t){const e=this._impl.open(t);return Sn(e)?e.then((()=>this)):this}readRecordBatch(t){return this._impl.isFile()?this._impl.readRecordBatch(t):null}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}toDOMStream(){return se.toDOMStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this})}toNodeStream(){return se.toNodeStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this},{objectMode:!0})}static throughNode(t){throw new Error('"throughNode" not available in this environment')}static throughDOM(t,e){throw new Error('"throughDOM" not available in this environment')}static from(t){return t instanceof ce?t:vs(t)?hd(t):va(t)?pd(t):Sn(t)?Y(this,void 0,void 0,(function*(){return yield ce.from(yield t)})):Sa(t)||js(t)||Ea(t)||ei(t)?fd(new Xn(t)):dd(new Ur(t))}static readAll(t){return t instanceof ce?t.isSync()?oa(t):aa(t):vs(t)||ArrayBuffer.isView(t)||$i(t)||Ia(t)?oa(t):aa(t)}}class Pr extends ce{constructor(t){super(t),this._impl=t}readAll(){return[...this]}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return Ae(this,arguments,(function*(){yield q(yield*Xi(In(this[Symbol.iterator]())))}))}}class kr extends ce{constructor(t){super(t),this._impl=t}readAll(){return Y(this,void 0,void 0,(function*(){var t,e,i,r;const n=new Array;try{for(var s,o=!0,a=In(this);!(t=(s=yield a.next()).done);o=!0){r=s.value,o=!1;const t=r;n.push(t)}}catch(t){e={error:t}}finally{try{!o&&!t&&(i=a.return)&&(yield i.call(a))}finally{if(e)throw e.error}}return n}))}[Symbol.iterator](){throw new Error("AsyncRecordBatchStreamReader is not Iterable")}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}}class gl extends Pr{constructor(t){super(t),this._impl=t}}class cd extends kr{constructor(t){super(t),this._impl=t}}class bl{get numDictionaries(){return this._dictionaryIndex}get numRecordBatches(){return this._recordBatchIndex}constructor(t=new Map){this.closed=!1,this.autoDestroy=!0,this._dictionaryIndex=0,this._recordBatchIndex=0,this.dictionaries=t}isSync(){return!1}isAsync(){return!1}isFile(){return!1}isStream(){return!1}reset(t){return this._dictionaryIndex=0,this._recordBatchIndex=0,this.schema=t,this.dictionaries=new Map,this}_loadRecordBatch(t,e){const i=this._loadVectors(t,e,this.schema.fields),r=X({type:new zt(this.schema.fields),length:t.length,children:i});return new Qt(this.schema,r)}_loadDictionaryBatch(t,e){const{id:i,isDelta:r}=t,{dictionaries:n,schema:s}=this,o=n.get(i),a=s.dictionaries.get(i),l=this._loadVectors(t.data,e,[a]);return(o&&r?o.concat(new ct(l)):new ct(l)).memoize()}_loadVectors(t,e,i){return new ol(e,t.nodes,t.buffers,this.dictionaries,this.schema.metadataVersion).visitMany(i)}}class $r extends bl{constructor(t,e){super(e),this._reader=vs(t)?new od(this._handle=t):new yl(this._handle=t)}isSync(){return!0}isStream(){return!0}[Symbol.iterator](){return this}cancel(){!this.closed&&(this.closed=!0)&&(this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}open(t){return this.closed||(this.autoDestroy=wl(this,t),this.schema||(this.schema=this._reader.readSchema())||this.cancel()),this}throw(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.throw(t):Et}return(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.return(t):Et}next(){if(this.closed)return Et;let t;const{_reader:e}=this;for(;t=this._readNextMessageAndValidate();)if(t.isSchema())this.reset(t.header());else{if(t.isRecordBatch()){this._recordBatchIndex++;const i=t.header(),r=e.readMessageBody(t.bodyLength);return{done:!1,value:this._loadRecordBatch(i,r)}}if(t.isDictionaryBatch()){this._dictionaryIndex++;const i=t.header(),r=e.readMessageBody(t.bodyLength),n=this._loadDictionaryBatch(i,r);this.dictionaries.set(i.id,n)}}return this.schema&&0===this._recordBatchIndex?(this._recordBatchIndex++,{done:!1,value:new io(this.schema)}):this.return()}_readNextMessageAndValidate(t){return this._reader.readMessage(t)}}class jr extends bl{constructor(t,e){super(e),this._reader=new sd(this._handle=t)}isAsync(){return!0}isStream(){return!0}[Symbol.asyncIterator](){return this}cancel(){return Y(this,void 0,void 0,(function*(){!this.closed&&(this.closed=!0)&&(yield this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}))}open(t){return Y(this,void 0,void 0,(function*(){return this.closed||(this.autoDestroy=wl(this,t),this.schema||(this.schema=yield this._reader.readSchema())||(yield this.cancel())),this}))}throw(t){return Y(this,void 0,void 0,(function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.throw(t):Et}))}return(t){return Y(this,void 0,void 0,(function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.return(t):Et}))}next(){return Y(this,void 0,void 0,(function*(){if(this.closed)return Et;let t;const{_reader:e}=this;for(;t=yield this._readNextMessageAndValidate();)if(t.isSchema())yield this.reset(t.header());else{if(t.isRecordBatch()){this._recordBatchIndex++;const i=t.header(),r=yield e.readMessageBody(t.bodyLength);return{done:!1,value:this._loadRecordBatch(i,r)}}if(t.isDictionaryBatch()){this._dictionaryIndex++;const i=t.header(),r=yield e.readMessageBody(t.bodyLength),n=this._loadDictionaryBatch(i,r);this.dictionaries.set(i.id,n)}}return this.schema&&0===this._recordBatchIndex?(this._recordBatchIndex++,{done:!1,value:new io(this.schema)}):yield this.return()}))}_readNextMessageAndValidate(t){return Y(this,void 0,void 0,(function*(){return yield this._reader.readMessage(t)}))}}class _l extends $r{get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}constructor(t,e){super(t instanceof Zo?t:new Zo(t),e)}isSync(){return!0}isFile(){return!0}open(t){if(!this.closed&&!this._footer){this.schema=(this._footer=this._readFooter()).schema;for(const t of this._footer.dictionaryBatches())t&&this._readDictionaryBatch(this._dictionaryIndex++)}return super.open(t)}readRecordBatch(t){var e;if(this.closed)return null;this._footer||this.open();const i=null===(e=this._footer)||void 0===e?void 0:e.getRecordBatch(t);if(i&&this._handle.seek(i.offset)){const t=this._reader.readMessage(at.RecordBatch);if(null!=t&&t.isRecordBatch()){const e=t.header(),i=this._reader.readMessageBody(t.bodyLength);return this._loadRecordBatch(e,i)}}return null}_readDictionaryBatch(t){var e;const i=null===(e=this._footer)||void 0===e?void 0:e.getDictionaryBatch(t);if(i&&this._handle.seek(i.offset)){const t=this._reader.readMessage(at.DictionaryBatch);if(null!=t&&t.isDictionaryBatch()){const e=t.header(),i=this._reader.readMessageBody(t.bodyLength),r=this._loadDictionaryBatch(e,i);this.dictionaries.set(e.id,r)}}}_readFooter(){const{_handle:t}=this,e=t.size-ml,i=t.readInt32(e),r=t.readAt(e-i,i);return Ai.decode(r)}_readNextMessageAndValidate(t){var e;if(this._footer||this.open(),this._footer&&this._recordBatchIndex<this.numRecordBatches){const i=null===(e=this._footer)||void 0===e?void 0:e.getRecordBatch(this._recordBatchIndex);if(i&&this._handle.seek(i.offset))return this._reader.readMessage(t)}return null}}class ld extends jr{get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}constructor(t,...e){const i="number"!=typeof e[0]?e.shift():void 0,r=e[0]instanceof Map?e.shift():void 0;super(t instanceof xr?t:new xr(t,i),r)}isFile(){return!0}isAsync(){return!0}open(t){const e=Object.create(null,{open:{get:()=>super.open}});return Y(this,void 0,void 0,(function*(){if(!this.closed&&!this._footer){this.schema=(this._footer=yield this._readFooter()).schema;for(const t of this._footer.dictionaryBatches())t&&(yield this._readDictionaryBatch(this._dictionaryIndex++))}return yield e.open.call(this,t)}))}readRecordBatch(t){return Y(this,void 0,void 0,(function*(){var e;if(this.closed)return null;this._footer||(yield this.open());const i=null===(e=this._footer)||void 0===e?void 0:e.getRecordBatch(t);if(i&&(yield this._handle.seek(i.offset))){const t=yield this._reader.readMessage(at.RecordBatch);if(null!=t&&t.isRecordBatch()){const e=t.header(),i=yield this._reader.readMessageBody(t.bodyLength);return this._loadRecordBatch(e,i)}}return null}))}_readDictionaryBatch(t){return Y(this,void 0,void 0,(function*(){var e;const i=null===(e=this._footer)||void 0===e?void 0:e.getDictionaryBatch(t);if(i&&(yield this._handle.seek(i.offset))){const t=yield this._reader.readMessage(at.DictionaryBatch);if(null!=t&&t.isDictionaryBatch()){const e=t.header(),i=yield this._reader.readMessageBody(t.bodyLength),r=this._loadDictionaryBatch(e,i);this.dictionaries.set(e.id,r)}}}))}_readFooter(){return Y(this,void 0,void 0,(function*(){const{_handle:t}=this;t._pending&&(yield t._pending);const e=t.size-ml,i=yield t.readInt32(e),r=yield t.readAt(e-i,i);return Ai.decode(r)}))}_readNextMessageAndValidate(t){return Y(this,void 0,void 0,(function*(){if(this._footer||(yield this.open()),this._footer&&this._recordBatchIndex<this.numRecordBatches){const e=this._footer.getRecordBatch(this._recordBatchIndex);if(e&&(yield this._handle.seek(e.offset)))return yield this._reader.readMessage(t)}return null}))}}class ud extends $r{constructor(t,e){super(t,e)}_loadVectors(t,e,i){return new Sh(e,t.nodes,t.buffers,this.dictionaries,this.schema.metadataVersion).visitMany(i)}}function wl(t,e){return e&&"boolean"==typeof e.autoDestroy?e.autoDestroy:t.autoDestroy}function*oa(t){const e=ce.from(t);try{if(!e.open({autoDestroy:!1}).closed)do{yield e}while(!e.reset().open().closed)}finally{e.cancel()}}function aa(t){return Ae(this,arguments,(function*(){const e=yield q(ce.from(t));try{if(!(yield q(e.open({autoDestroy:!1}))).closed)do{yield yield q(e)}while(!(yield q(e.reset().open())).closed)}finally{yield q(e.cancel())}}))}function hd(t){return new Pr(new ud(t))}function dd(t){const e=t.peek(Wi+7&-8);return e&&e.byteLength>=4?oo(e)?new gl(new _l(t.read())):new Pr(new $r(t)):new Pr(new $r(function*(){}()))}function fd(t){return Y(this,void 0,void 0,(function*(){const e=yield t.peek(Wi+7&-8);return e&&e.byteLength>=4?oo(e)?new gl(new _l(yield t.read())):new kr(new jr(t)):new kr(new jr(function(){return Ae(this,arguments,(function*(){}))}()))}))}function pd(t){return Y(this,void 0,void 0,(function*(){const{size:e}=yield t.stat(),i=new xr(t,e);return e>=ad&&oo(yield i.readAt(0,Wi+7&-8))?new cd(new ld(i)):new kr(new jr(i))}))}class Tt extends J{static assemble(...t){const e=t=>t.flatMap((t=>Array.isArray(t)?e(t):t instanceof Qt?t.data.children:t.data)),i=new Tt;return i.visitMany(e(t)),i}constructor(){super(),this._byteLength=0,this._nodes=[],this._buffers=[],this._bufferRegions=[]}visit(t){if(t instanceof ct)return this.visitMany(t.data),this;const{type:e}=t;if(!D.isDictionary(e)){const{length:i}=t;if(i>2147483647)throw new RangeError("Cannot write arrays larger than 2^31 - 1 in length");if(D.isUnion(e))this.nodes.push(new an(i,0));else{const{nullCount:r}=t;D.isNull(e)||ge.call(this,r<=0?new Uint8Array(0):Br(t.offset,i,t.nullBitmap)),this.nodes.push(new an(i,r))}}return super.visit(t)}visitNull(t){return this}visitDictionary(t){return this.visit(t.clone(t.type.indices))}get nodes(){return this._nodes}get buffers(){return this._buffers}get byteLength(){return this._byteLength}get bufferRegions(){return this._bufferRegions}}function ge(t){const e=t.byteLength+7&-8;return this.buffers.push(t),this.bufferRegions.push(new Fe(this._byteLength,e)),this._byteLength+=e,this}function yd(t){var e;const{type:i,length:r,typeIds:n,valueOffsets:s}=t;if(ge.call(this,n),i.mode===Pt.Sparse)return Ms.call(this,t);if(i.mode===Pt.Dense){if(t.offset<=0)return ge.call(this,s),Ms.call(this,t);{const o=new Int32Array(r),a=Object.create(null),l=Object.create(null);for(let t,i,c=-1;++c<r;)void 0!==(t=n[c])&&(void 0===(i=a[t])&&(i=a[t]=s[c]),o[c]=s[c]-i,l[t]=(null!==(e=l[t])&&void 0!==e?e:0)+1);ge.call(this,o),this.visitMany(t.children.map(((t,e)=>{const n=i.typeIds[e],s=a[n],o=l[n];return t.slice(s,Math.min(r,o))})))}}return this}function md(t){let e;return t.nullCount>=t.length?ge.call(this,new Uint8Array(0)):(e=t.values)instanceof Uint8Array?ge.call(this,Br(t.offset,t.length,e)):ge.call(this,Rr(t.values))}function We(t){return ge.call(this,t.values.subarray(0,t.length*t.stride))}function es(t){const{length:e,values:i,valueOffsets:r}=t,n=pt(r[0]),s=pt(r[e]),o=Math.min(s-n,i.byteLength-n);return ge.call(this,Aa(-n,e+1,r)),ge.call(this,i.subarray(n,n+o)),this}function ao(t){const{length:e,valueOffsets:i}=t;if(i){const{0:r,[e]:n}=i;return ge.call(this,Aa(-r,e+1,i)),this.visit(t.children[0].slice(r,n-r))}return this.visit(t.children[0])}function Ms(t){return this.visitMany(t.type.children.map(((e,i)=>t.children[i])).filter(Boolean))[0]}Tt.prototype.visitBool=md,Tt.prototype.visitInt=We,Tt.prototype.visitFloat=We,Tt.prototype.visitUtf8=es,Tt.prototype.visitLargeUtf8=es,Tt.prototype.visitBinary=es,Tt.prototype.visitLargeBinary=es,Tt.prototype.visitFixedSizeBinary=We,Tt.prototype.visitDate=We,Tt.prototype.visitTimestamp=We,Tt.prototype.visitTime=We,Tt.prototype.visitDecimal=We,Tt.prototype.visitList=ao,Tt.prototype.visitStruct=Ms,Tt.prototype.visitUnion=yd,Tt.prototype.visitInterval=We,Tt.prototype.visitDuration=We,Tt.prototype.visitFixedSizeList=ao,Tt.prototype.visitMap=ao;class Il extends Zs{static throughNode(t){throw new Error('"throughNode" not available in this environment')}static throughDOM(t,e){throw new Error('"throughDOM" not available in this environment')}constructor(t){super(),this._position=0,this._started=!1,this._sink=new or,this._schema=null,this._dictionaryBlocks=[],this._recordBatchBlocks=[],this._seenDictionaries=new Map,this._dictionaryDeltaOffsets=new Map,Jt(t)||(t={autoDestroy:!0,writeLegacyIpcFormat:!1}),this._autoDestroy="boolean"!=typeof t.autoDestroy||t.autoDestroy,this._writeLegacyIpcFormat="boolean"==typeof t.writeLegacyIpcFormat&&t.writeLegacyIpcFormat}toString(t=!1){return this._sink.toString(t)}toUint8Array(t=!1){return this._sink.toUint8Array(t)}writeAll(t){return Sn(t)?t.then((t=>this.writeAll(t))):ei(t)?ho(this,t):uo(this,t)}get closed(){return this._sink.closed}[Symbol.asyncIterator](){return this._sink[Symbol.asyncIterator]()}toDOMStream(t){return this._sink.toDOMStream(t)}toNodeStream(t){return this._sink.toNodeStream(t)}close(){return this.reset()._sink.close()}abort(t){return this.reset()._sink.abort(t)}finish(){return this._autoDestroy?this.close():this.reset(this._sink,this._schema),this}reset(t=this._sink,e=null){return t===this._sink||t instanceof or?this._sink=t:(this._sink=new or,t&&ql(t)?this.toDOMStream({type:"bytes"}).pipeTo(t):t&&Hl(t)&&this.toNodeStream({objectMode:!1}).pipe(t)),this._started&&this._schema&&this._writeFooter(this._schema),this._started=!1,this._dictionaryBlocks=[],this._recordBatchBlocks=[],this._seenDictionaries=new Map,this._dictionaryDeltaOffsets=new Map,(!e||!Bs(e,this._schema))&&(null==e?(this._position=0,this._schema=null):(this._started=!0,this._schema=e,this._writeSchema(e))),this}write(t){let e=null;if(!this._sink)throw new Error("RecordBatchWriter is closed");if(null==t)return this.finish()&&void 0;if(t instanceof Vt&&!(e=t.schema))return this.finish()&&void 0;if(t instanceof Qt&&!(e=t.schema))return this.finish()&&void 0;if(e&&!Bs(e,this._schema)){if(this._started&&this._autoDestroy)return this.close();this.reset(this._sink,e)}t instanceof Qt?t instanceof io||this._writeRecordBatch(t):t instanceof Vt?this.writeAll(t.batches):$i(t)&&this.writeAll(t)}_writeMessage(t,e=8){const i=e-1,r=Wt.encode(t),n=r.byteLength,s=this._writeLegacyIpcFormat?4:8,o=n+s+i&~i,a=o-n-s;return t.headerType===at.RecordBatch?this._recordBatchBlocks.push(new sn(o,t.bodyLength,this._position)):t.headerType===at.DictionaryBatch&&this._dictionaryBlocks.push(new sn(o,t.bodyLength,this._position)),this._writeLegacyIpcFormat||this._write(Int32Array.of(-1)),this._write(Int32Array.of(o-s)),n>0&&this._write(r),this._writePadding(a)}_write(t){if(this._started){const e=rt(t);e&&e.byteLength>0&&(this._sink.write(e),this._position+=e.byteLength)}return this}_writeSchema(t){return this._writeMessage(Wt.from(t))}_writeFooter(t){return this._writeLegacyIpcFormat?this._write(Int32Array.of(0)):this._write(Int32Array.of(-1,0))}_writeMagic(){return this._write(Fi)}_writePadding(t){return t>0?this._write(new Uint8Array(t)):this}_writeRecordBatch(t){const{byteLength:e,nodes:i,bufferRegions:r,buffers:n}=Tt.assemble(t),s=new ee(t.numRows,i,r),o=Wt.from(s,e);return this._writeDictionaries(t)._writeMessage(o)._writeBodyBuffers(n)}_writeDictionaryBatch(t,e,i=!1){const{byteLength:r,nodes:n,bufferRegions:s,buffers:o}=Tt.assemble(new ct([t])),a=new ee(t.length,n,s),l=new Ne(a,e,i),c=Wt.from(l,r);return this._writeMessage(c)._writeBodyBuffers(o)}_writeBodyBuffers(t){let e,i,r;for(let n=-1,s=t.length;++n<s;)(e=t[n])&&(i=e.byteLength)>0&&(this._write(e),(r=(i+7&-8)-i)>0&&this._writePadding(r));return this}_writeDictionaries(t){var e,i;for(const[r,n]of t.dictionaries){const t=null!==(e=null==n?void 0:n.data)&&void 0!==e?e:[],s=this._seenDictionaries.get(r),o=null!==(i=this._dictionaryDeltaOffsets.get(r))&&void 0!==i?i:0;if(s&&s.data[0]===t[0]){if(o<t.length)for(const e of t.slice(o))this._writeDictionaryBatch(e,r,!0)}else for(const[e,i]of t.entries())this._writeDictionaryBatch(i,r,e>0);this._seenDictionaries.set(r,n),this._dictionaryDeltaOffsets.set(r,t.length)}return this}}class co extends Il{static writeAll(t,e){const i=new co(e);return Sn(t)?t.then((t=>i.writeAll(t))):ei(t)?ho(i,t):uo(i,t)}}class lo extends Il{static writeAll(t){const e=new lo;return Sn(t)?t.then((t=>e.writeAll(t))):ei(t)?ho(e,t):uo(e,t)}constructor(){super(),this._autoDestroy=!0}_writeSchema(t){return this._writeMagic()._writePadding(2)}_writeDictionaryBatch(t,e,i=!1){if(!i&&this._seenDictionaries.has(e))throw new Error("The Arrow File format does not support replacement dictionaries. ");return super._writeDictionaryBatch(t,e,i)}_writeFooter(t){const e=Ai.encode(new Ai(t,Dt.V5,this._recordBatchBlocks,this._dictionaryBlocks));return super._writeFooter(t)._write(e)._write(Int32Array.of(e.byteLength))._writeMagic()}}function uo(t,e){let i=e;e instanceof Vt&&(i=e.batches,t.reset(void 0,e.schema));for(const e of i)t.write(e);return t.finish()}function ho(t,e){return Y(this,void 0,void 0,(function*(){var i,r,n,s,o,a,l;try{for(i=!0,r=In(e);!(s=(n=yield r.next()).done);i=!0){l=n.value,i=!1;const e=l;t.write(e)}}catch(t){o={error:t}}finally{try{!i&&!s&&(a=r.return)&&(yield a.call(r))}finally{if(o)throw o.error}}return t.finish()}))}function gd(t,e="stream"){return("stream"===e?co:lo).writeAll(t).toUint8Array(!0)}var bd=Object.create,vl=Object.defineProperty,_d=Object.getOwnPropertyDescriptor,wd=Object.getOwnPropertyNames,Id=Object.getPrototypeOf,vd=Object.prototype.hasOwnProperty,Sd=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),Ed=(t,e,i,r)=>{if(e&&"object"==typeof e||"function"==typeof e)for(let n of wd(e))!vd.call(t,n)&&n!==i&&vl(t,n,{get:()=>e[n],enumerable:!(r=_d(e,n))||r.enumerable});return t},Td=(t,e,i)=>(i=null!=t?bd(Id(t)):{},Ed(t&&t.__esModule?i:vl(i,"default",{value:t,enumerable:!0}),t)),Ad=Sd(((t,e)=>{e.exports=Worker})),Fd=(t=>(t[t.UNDEFINED=0]="UNDEFINED",t[t.AUTOMATIC=1]="AUTOMATIC",t[t.READ_ONLY=2]="READ_ONLY",t[t.READ_WRITE=3]="READ_WRITE",t))(Fd||{}),Od=(t=>(t[t.IDENTIFIER=0]="IDENTIFIER",t[t.NUMERIC_CONSTANT=1]="NUMERIC_CONSTANT",t[t.STRING_CONSTANT=2]="STRING_CONSTANT",t[t.OPERATOR=3]="OPERATOR",t[t.KEYWORD=4]="KEYWORD",t[t.COMMENT=5]="COMMENT",t))(Od||{}),Dd=(t=>(t[t.NONE=0]="NONE",t[t.DEBUG=1]="DEBUG",t[t.INFO=2]="INFO",t[t.WARNING=3]="WARNING",t[t.ERROR=4]="ERROR",t))(Dd||{}),Nd=(t=>(t[t.NONE=0]="NONE",t[t.CONNECT=1]="CONNECT",t[t.DISCONNECT=2]="DISCONNECT",t[t.OPEN=3]="OPEN",t[t.QUERY=4]="QUERY",t[t.INSTANTIATE=5]="INSTANTIATE",t))(Nd||{}),Bd=(t=>(t[t.NONE=0]="NONE",t[t.OK=1]="OK",t[t.ERROR=2]="ERROR",t[t.START=3]="START",t[t.RUN=4]="RUN",t[t.CAPTURE=5]="CAPTURE",t))(Bd||{}),Rd=(t=>(t[t.NONE=0]="NONE",t[t.WEB_WORKER=1]="WEB_WORKER",t[t.NODE_WORKER=2]="NODE_WORKER",t[t.BINDINGS=3]="BINDINGS",t[t.ASYNC_DUCKDB=4]="ASYNC_DUCKDB",t))(Rd||{}),Md=class{log(t){}},Cd=(t=>(t[t.SUCCESS=0]="SUCCESS",t))(Cd||{}),Ld=class{constructor(t,e){this._bindings=t,this._conn=e}get bindings(){return this._bindings}async close(){return this._bindings.disconnect(this._conn)}useUnsafe(t){return t(this._bindings,this._conn)}async query(t){this._bindings.logger.log({timestamp:new Date,level:2,origin:4,topic:4,event:4,value:t});let e=await this._bindings.runQuery(this._conn,t),i=ce.from(e);return console.assert(i.isSync(),"Reader is not sync"),console.assert(i.isFile(),"Reader is not file"),new Vt(i)}async send(t){this._bindings.logger.log({timestamp:new Date,level:2,origin:4,topic:4,event:4,value:t});let e=await this._bindings.startPendingQuery(this._conn,t);for(;null==e;)e=await this._bindings.pollPendingQuery(this._conn);let i=new Sl(this._bindings,this._conn,e),r=await ce.from(i);return console.assert(r.isAsync()),console.assert(r.isStream()),r}async cancelSent(){return await this._bindings.cancelPendingQuery(this._conn)}async getTableNames(t){return await this._bindings.getTableNames(this._conn,t)}async prepare(t){let e=await this._bindings.createPrepared(this._conn,t);return new Ud(this._bindings,this._conn,e)}async insertArrowTable(t,e){let i=gd(t,"stream");await this.insertArrowFromIPCStream(i,e)}async insertArrowFromIPCStream(t,e){await this._bindings.insertArrowFromIPCStream(this._conn,t,e)}async insertCSVFromPath(t,e){await this._bindings.insertCSVFromPath(this._conn,t,e)}async insertJSONFromPath(t,e){await this._bindings.insertJSONFromPath(this._conn,t,e)}},Sl=class{constructor(t,e,i){this.db=t,this.conn=e,this.header=i,this._first=!0,this._depleted=!1,this._inFlight=null}async next(){if(this._first)return this._first=!1,{done:!1,value:this.header};if(this._depleted)return{done:!0,value:null};let t;return null!=this._inFlight?(t=await this._inFlight,this._inFlight=null):t=await this.db.fetchQueryResults(this.conn),this._depleted=0==t.length,this._depleted||(this._inFlight=this.db.fetchQueryResults(this.conn)),{done:this._depleted,value:t}}[Symbol.asyncIterator](){return this}},Ud=class{constructor(t,e,i){this.bindings=t,this.connectionId=e,this.statementId=i}async close(){await this.bindings.closePrepared(this.connectionId,this.statementId)}async query(...t){let e=await this.bindings.runPrepared(this.connectionId,this.statementId,t),i=ce.from(e);return console.assert(i.isSync()),console.assert(i.isFile()),new Vt(i)}async send(...t){let e=await this.bindings.sendPrepared(this.connectionId,this.statementId,t),i=new Sl(this.bindings,this.connectionId,e),r=await ce.from(i);return console.assert(r.isAsync()),console.assert(r.isStream()),r}},xd=(t=>(t.CANCEL_PENDING_QUERY="CANCEL_PENDING_QUERY",t.CLOSE_PREPARED="CLOSE_PREPARED",t.COLLECT_FILE_STATISTICS="COLLECT_FILE_STATISTICS",t.CONNECT="CONNECT",t.COPY_FILE_TO_BUFFER="COPY_FILE_TO_BUFFER",t.COPY_FILE_TO_PATH="COPY_FILE_TO_PATH",t.CREATE_PREPARED="CREATE_PREPARED",t.DISCONNECT="DISCONNECT",t.DROP_FILE="DROP_FILE",t.DROP_FILES="DROP_FILES",t.EXPORT_FILE_STATISTICS="EXPORT_FILE_STATISTICS",t.FETCH_QUERY_RESULTS="FETCH_QUERY_RESULTS",t.FLUSH_FILES="FLUSH_FILES",t.GET_FEATURE_FLAGS="GET_FEATURE_FLAGS",t.GET_TABLE_NAMES="GET_TABLE_NAMES",t.GET_VERSION="GET_VERSION",t.GLOB_FILE_INFOS="GLOB_FILE_INFOS",t.INSERT_ARROW_FROM_IPC_STREAM="INSERT_ARROW_FROM_IPC_STREAM",t.INSERT_CSV_FROM_PATH="IMPORT_CSV_FROM_PATH",t.INSERT_JSON_FROM_PATH="IMPORT_JSON_FROM_PATH",t.INSTANTIATE="INSTANTIATE",t.OPEN="OPEN",t.PING="PING",t.POLL_PENDING_QUERY="POLL_PENDING_QUERY",t.REGISTER_FILE_BUFFER="REGISTER_FILE_BUFFER",t.REGISTER_FILE_HANDLE="REGISTER_FILE_HANDLE",t.REGISTER_FILE_URL="REGISTER_FILE_URL",t.RESET="RESET",t.RUN_PREPARED="RUN_PREPARED",t.RUN_QUERY="RUN_QUERY",t.SEND_PREPARED="SEND_PREPARED",t.START_PENDING_QUERY="START_PENDING_QUERY",t.TOKENIZE="TOKENIZE",t))(xd||{}),Pd=(t=>(t.CONNECTION_INFO="CONNECTION_INFO",t.ERROR="ERROR",t.FEATURE_FLAGS="FEATURE_FLAGS",t.FILE_BUFFER="FILE_BUFFER",t.FILE_INFOS="FILE_INFOS",t.FILE_SIZE="FILE_SIZE",t.FILE_STATISTICS="FILE_STATISTICS",t.INSTANTIATE_PROGRESS="INSTANTIATE_PROGRESS",t.LOG="LOG",t.OK="OK",t.PREPARED_STATEMENT_ID="PREPARED_STATEMENT_ID",t.QUERY_PLAN="QUERY_PLAN",t.QUERY_RESULT="QUERY_RESULT",t.QUERY_RESULT_CHUNK="QUERY_RESULT_CHUNK",t.QUERY_RESULT_HEADER="QUERY_RESULT_HEADER",t.QUERY_RESULT_HEADER_OR_NULL="QUERY_RESULT_HEADER_OR_NULL",t.REGISTERED_FILE="REGISTERED_FILE",t.SCRIPT_TOKENS="SCRIPT_TOKENS",t.SUCCESS="SUCCESS",t.TABLE_NAMES="TABLE_NAMES",t.VERSION_STRING="VERSION_STRING",t))(Pd||{}),ot=class{constructor(t,e){this.promiseResolver=()=>{},this.promiseRejecter=()=>{},this.type=t,this.data=e,this.promise=new Promise(((t,e)=>{this.promiseResolver=t,this.promiseRejecter=e}))}};function lr(t){switch(t.typeId){case h.Binary:return{sqlType:"binary"};case h.Bool:return{sqlType:"bool"};case h.Date:return{sqlType:"date"};case h.DateDay:return{sqlType:"date32[d]"};case h.DateMillisecond:return{sqlType:"date64[ms]"};case h.Decimal:{let e=t;return{sqlType:"decimal",precision:e.precision,scale:e.scale}}case h.Float:return{sqlType:"float"};case h.Float16:return{sqlType:"float16"};case h.Float32:return{sqlType:"float32"};case h.Float64:return{sqlType:"float64"};case h.Int:return{sqlType:"int32"};case h.Int16:return{sqlType:"int16"};case h.Int32:return{sqlType:"int32"};case h.Int64:return{sqlType:"int64"};case h.Uint16:return{sqlType:"uint16"};case h.Uint32:return{sqlType:"uint32"};case h.Uint64:return{sqlType:"uint64"};case h.Uint8:return{sqlType:"uint8"};case h.IntervalDayTime:return{sqlType:"interval[dt]"};case h.IntervalYearMonth:return{sqlType:"interval[m]"};case h.List:return{sqlType:"list",valueType:lr(t.valueType)};case h.FixedSizeBinary:return{sqlType:"fixedsizebinary",byteWidth:t.byteWidth};case h.Null:return{sqlType:"null"};case h.Utf8:return{sqlType:"utf8"};case h.Struct:return{sqlType:"struct",fields:t.children.map((t=>Cs(t.name,t.type)))};case h.Map:{let e=t;return{sqlType:"map",keyType:lr(e.keyType),valueType:lr(e.valueType)}}case h.Time:return{sqlType:"time[s]"};case h.TimeMicrosecond:return{sqlType:"time[us]"};case h.TimeMillisecond:return{sqlType:"time[ms]"};case h.TimeNanosecond:return{sqlType:"time[ns]"};case h.TimeSecond:return{sqlType:"time[s]"};case h.Timestamp:return{sqlType:"timestamp",timezone:t.timezone||void 0};case h.TimestampSecond:return{sqlType:"timestamp[s]",timezone:t.timezone||void 0};case h.TimestampMicrosecond:return{sqlType:"timestamp[us]",timezone:t.timezone||void 0};case h.TimestampNanosecond:return{sqlType:"timestamp[ns]",timezone:t.timezone||void 0};case h.TimestampMillisecond:return{sqlType:"timestamp[ms]",timezone:t.timezone||void 0}}throw new Error("unsupported arrow type: ".concat(t.toString()))}function Cs(t,e){let i=lr(e);return i.name=t,i}var kd=new TextEncoder,$d=class{constructor(t,e=null){this._onInstantiationProgress=[],this._worker=null,this._workerShutdownPromise=null,this._workerShutdownResolver=()=>{},this._nextMessageId=0,this._pendingRequests=new Map,this._logger=t,this._onMessageHandler=this.onMessage.bind(this),this._onErrorHandler=this.onError.bind(this),this._onCloseHandler=this.onClose.bind(this),null!=e&&this.attach(e)}get logger(){return this._logger}attach(t){this._worker=t,this._worker.addEventListener("message",this._onMessageHandler),this._worker.addEventListener("error",this._onErrorHandler),this._worker.addEventListener("close",this._onCloseHandler),this._workerShutdownPromise=new Promise(((t,e)=>{this._workerShutdownResolver=t}))}detach(){this._worker&&(this._worker.removeEventListener("message",this._onMessageHandler),this._worker.removeEventListener("error",this._onErrorHandler),this._worker.removeEventListener("close",this._onCloseHandler),this._worker=null,this._workerShutdownResolver(null),this._workerShutdownPromise=null,this._workerShutdownResolver=()=>{})}async terminate(){this._worker&&(this._worker.terminate(),this._worker=null,this._workerShutdownPromise=null,this._workerShutdownResolver=()=>{})}async postTask(t,e=[]){if(!this._worker)return void console.error("cannot send a message since the worker is not set!");let i=this._nextMessageId++;return this._pendingRequests.set(i,t),this._worker.postMessage({messageId:i,type:t.type,data:t.data},e),await t.promise}onMessage(t){var e;let i=t.data;switch(i.type){case"LOG":return void this._logger.log(i.data);case"INSTANTIATE_PROGRESS":for(let t of this._onInstantiationProgress)t(i.data);return}let r=this._pendingRequests.get(i.requestId);if(r){if(this._pendingRequests.delete(i.requestId),"ERROR"==i.type){let t=new Error(i.data.message);return t.name=i.data.name,null!=(e=Object.getOwnPropertyDescriptor(t,"stack"))&&e.writable&&(t.stack=i.data.stack),void r.promiseRejecter(t)}switch(r.type){case"CLOSE_PREPARED":case"COLLECT_FILE_STATISTICS":case"COPY_FILE_TO_PATH":case"DISCONNECT":case"DROP_FILE":case"DROP_FILES":case"FLUSH_FILES":case"INSERT_ARROW_FROM_IPC_STREAM":case"IMPORT_CSV_FROM_PATH":case"IMPORT_JSON_FROM_PATH":case"OPEN":case"PING":case"REGISTER_FILE_BUFFER":case"REGISTER_FILE_HANDLE":case"REGISTER_FILE_URL":case"RESET":if("OK"==i.type)return void r.promiseResolver(i.data);break;case"INSTANTIATE":if(this._onInstantiationProgress=[],"OK"==i.type)return void r.promiseResolver(i.data);break;case"GLOB_FILE_INFOS":if("FILE_INFOS"==i.type)return void r.promiseResolver(i.data);break;case"GET_VERSION":if("VERSION_STRING"==i.type)return void r.promiseResolver(i.data);break;case"GET_FEATURE_FLAGS":if("FEATURE_FLAGS"==i.type)return void r.promiseResolver(i.data);break;case"GET_TABLE_NAMES":if("TABLE_NAMES"==i.type)return void r.promiseResolver(i.data);break;case"TOKENIZE":if("SCRIPT_TOKENS"==i.type)return void r.promiseResolver(i.data);break;case"COPY_FILE_TO_BUFFER":if("FILE_BUFFER"==i.type)return void r.promiseResolver(i.data);break;case"EXPORT_FILE_STATISTICS":if("FILE_STATISTICS"==i.type)return void r.promiseResolver(i.data);break;case"CONNECT":if("CONNECTION_INFO"==i.type)return void r.promiseResolver(i.data);break;case"RUN_PREPARED":case"RUN_QUERY":if("QUERY_RESULT"==i.type)return void r.promiseResolver(i.data);break;case"SEND_PREPARED":if("QUERY_RESULT_HEADER"==i.type)return void r.promiseResolver(i.data);break;case"START_PENDING_QUERY":case"POLL_PENDING_QUERY":if("QUERY_RESULT_HEADER_OR_NULL"==i.type)return void r.promiseResolver(i.data);break;case"CANCEL_PENDING_QUERY":if(this._onInstantiationProgress=[],"SUCCESS"==i.type)return void r.promiseResolver(i.data);break;case"FETCH_QUERY_RESULTS":if("QUERY_RESULT_CHUNK"==i.type)return void r.promiseResolver(i.data);break;case"CREATE_PREPARED":if("PREPARED_STATEMENT_ID"==i.type)return void r.promiseResolver(i.data)}r.promiseRejecter(new Error("unexpected response type: ".concat(i.type.toString())))}else console.warn("unassociated response: [".concat(i.requestId,", ").concat(i.type.toString(),"]"))}onError(t){console.error(t),console.error("error in duckdb worker: ".concat(t.message)),this._pendingRequests.clear()}onClose(){this._workerShutdownResolver(null),0==this._pendingRequests.size?this._pendingRequests.clear():console.warn("worker terminated with ".concat(this._pendingRequests.size," pending requests"))}async reset(){let t=new ot("RESET",null);return await this.postTask(t)}async ping(){let t=new ot("PING",null);await this.postTask(t)}async dropFile(t){let e=new ot("DROP_FILE",t);return await this.postTask(e)}async dropFiles(){let t=new ot("DROP_FILES",null);return await this.postTask(t)}async flushFiles(){let t=new ot("FLUSH_FILES",null);return await this.postTask(t)}async instantiate(t,e=null,i=t=>{}){this._onInstantiationProgress.push(i);let r=new ot("INSTANTIATE",[t,e]);return await this.postTask(r)}async getVersion(){let t=new ot("GET_VERSION",null);return await this.postTask(t)}async getFeatureFlags(){let t=new ot("GET_FEATURE_FLAGS",null);return await this.postTask(t)}async open(t){let e=new ot("OPEN",t);await this.postTask(e)}async tokenize(t){let e=new ot("TOKENIZE",t);return await this.postTask(e)}async connectInternal(){let t=new ot("CONNECT",null);return await this.postTask(t)}async connect(){let t=await this.connectInternal();return new Ld(this,t)}async disconnect(t){let e=new ot("DISCONNECT",t);await this.postTask(e)}async runQuery(t,e){let i=new ot("RUN_QUERY",[t,e]);return await this.postTask(i)}async startPendingQuery(t,e){let i=new ot("START_PENDING_QUERY",[t,e]);return await this.postTask(i)}async pollPendingQuery(t){let e=new ot("POLL_PENDING_QUERY",t);return await this.postTask(e)}async cancelPendingQuery(t){let e=new ot("CANCEL_PENDING_QUERY",t);return await this.postTask(e)}async fetchQueryResults(t){let e=new ot("FETCH_QUERY_RESULTS",t);return await this.postTask(e)}async getTableNames(t,e){let i=new ot("GET_TABLE_NAMES",[t,e]);return await this.postTask(i)}async createPrepared(t,e){let i=new ot("CREATE_PREPARED",[t,e]);return await this.postTask(i)}async closePrepared(t,e){let i=new ot("CLOSE_PREPARED",[t,e]);await this.postTask(i)}async runPrepared(t,e,i){let r=new ot("RUN_PREPARED",[t,e,i]);return await this.postTask(r)}async sendPrepared(t,e,i){let r=new ot("SEND_PREPARED",[t,e,i]);return await this.postTask(r)}async globFiles(t){let e=new ot("GLOB_FILE_INFOS",t);return await this.postTask(e)}async registerFileText(t,e){let i=kd.encode(e);await this.registerFileBuffer(t,i)}async registerFileURL(t,e,i,r){void 0===e&&(e=t);let n=new ot("REGISTER_FILE_URL",[t,e,i,r]);await this.postTask(n)}async registerEmptyFileBuffer(t){}async registerFileBuffer(t,e){let i=new ot("REGISTER_FILE_BUFFER",[t,e]);await this.postTask(i,[e.buffer])}async registerFileHandle(t,e,i,r){let n=new ot("REGISTER_FILE_HANDLE",[t,e,i,r]);await this.postTask(n,[])}async collectFileStatistics(t,e){let i=new ot("COLLECT_FILE_STATISTICS",[t,e]);await this.postTask(i,[])}async exportFileStatistics(t){let e=new ot("EXPORT_FILE_STATISTICS",t);return await this.postTask(e,[])}async copyFileToBuffer(t){let e=new ot("COPY_FILE_TO_BUFFER",t);return await this.postTask(e)}async copyFileToPath(t,e){let i=new ot("COPY_FILE_TO_PATH",[t,e]);await this.postTask(i)}async insertArrowFromIPCStream(t,e,i){if(0==e.length)return;let r=new ot("INSERT_ARROW_FROM_IPC_STREAM",[t,e,i]);await this.postTask(r,[e.buffer])}async insertCSVFromPath(t,e,i){if(void 0!==i.columns){let t=[];for(let e in i.columns){let r=i.columns[e];t.push(Cs(e,r))}i.columnsFlat=t,delete i.columns}let r=new ot("IMPORT_CSV_FROM_PATH",[t,e,i]);await this.postTask(r)}async insertJSONFromPath(t,e,i){if(void 0!==i.columns){let t=[];for(let e in i.columns){let r=i.columns[e];t.push(Cs(e,r))}i.columnsFlat=t,delete i.columns}let r=new ot("IMPORT_JSON_FROM_PATH",[t,e,i]);await this.postTask(r)}},jd=async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,5,3,1,0,1,10,14,1,12,0,65,0,65,0,65,0,252,10,0,0,11])),Vd=async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,8,1,6,0,6,64,25,11,11])),zd=async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,5,1,96,0,1,123,3,2,1,0,10,10,1,8,0,65,0,253,15,253,98,11])),Wd=()=>(async t=>{try{return typeof MessageChannel<"u"&&(new MessageChannel).port1.postMessage(new SharedArrayBuffer(1)),WebAssembly.validate(t)}catch{return!1}})(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,5,4,1,3,1,1,10,11,1,9,0,65,0,254,16,2,0,26,11])),Yd={version:"1.29.0"},fo=Yd.version.split(".");fo[0],fo[1],fo[2];var Gd=()=>typeof navigator>"u",hs=null,ds=null,fs=null,ps=null,ys=null;async function qd(){return null==hs&&(hs=typeof BigInt64Array<"u"),null==ds&&(ds=await Vd()),null==fs&&(fs=await Wd()),null==ps&&(ps=await zd()),null==ys&&(ys=await jd()),{bigInt64Array:hs,crossOriginIsolated:Gd()||globalThis.crossOriginIsolated||!1,wasmExceptions:ds,wasmSIMD:ps,wasmThreads:fs,wasmBulkMemory:ys}}function Hd(){let t=new TextDecoder;return e=>(typeof SharedArrayBuffer<"u"&&e.buffer instanceof SharedArrayBuffer&&(e=new Uint8Array(e)),t.decode(e))}Td(Ad()),Hd();var El=(t=>(t[t.BUFFER=0]="BUFFER",t[t.NODE_FS=1]="NODE_FS",t[t.BROWSER_FILEREADER=2]="BROWSER_FILEREADER",t[t.BROWSER_FSACCESS=3]="BROWSER_FSACCESS",t[t.HTTP=4]="HTTP",t[t.S3=5]="S3",t))(El||{});let me,vn;const{resolve:Qd,reject:Jd,promise:Kd}=ba(),{resolve:Zd,reject:Xd,promise:tf}=ba();let ca=!1;async function po(){if(!me){if(ca)return _a(Kd);ca=!0;try{const t=await qd().then((t=>t.wasmExceptions))?{mainModule:(await Hi((async()=>{const{default:t}=await import("./duckdb-eh.CjaN1hVf.js");return{default:t}}),[])).default,mainWorker:(await Hi((async()=>{const{default:t}=await import("./duckdb-browser-eh.worker.xVZH6Sl3.js");return{default:t}}),[])).default}:{mainModule:(await Hi((async()=>{const{default:t}=await import("./duckdb-mvp.Bsk8BUlZ.js");return{default:t}}),[])).default,mainWorker:(await Hi((async()=>{const{default:t}=await import("./duckdb-browser-mvp.worker.BgKlvv5e.js");return{default:t}}),[])).default},e=new Md,i=new t.mainWorker,r=new $d(e,i);window[Symbol.for("EVIDENCE_QUERY_ENGINE")]=r,await r.instantiate(t.mainModule),me=r,await me.open({query:{castBigIntToDouble:!0,castTimestampToDate:!0,castDecimalToDouble:!0,castDurationToTime64:!0}}),vn=await me.connect(),await vn.query("SET ieee_floating_point_ops = false;"),await vn.query("SET old_implicit_casting = true;"),Qd()}catch(t){throw Jd(t),t}}}async function qf(t){me||await po(),await vn.query(`PRAGMA search_path='${t.join(",")}'`)}async function ms(t){await me.flushFiles();for(const e of await me.globFiles(t))await me.dropFile(e.fileName)}async function Hf(t,{append:e,addBasePath:i=t=>t}={}){me||await po(),e||await ms("*");try{for(const r in t){await vn.query(`CREATE SCHEMA IF NOT EXISTS "${r}";`);for(const n of t[r]){const t=n.split(/[\\/]/).at(-1).slice(0,-8),s=`${r}_${t}.parquet`;let o=n;!n.startsWith("http")&&!n.startsWith("/")&&(o=`/${n}`),o.startsWith("/static")&&(o=o.substring(7)),e&&(await ms(s),await ms(n)),await me.registerFileURL(s,i(o),El.HTTP,!1),await vn.query(`CREATE OR REPLACE VIEW "${r}"."${t}" AS (SELECT * FROM read_parquet('${s}'));`)}}Zd()}catch(t){throw Xd(t),console.error("Error encountered while updating Parquet URLs",t),t}}async function ef(t){return me||await po(),await _a(tf),await vn.query(t).then(zl)}const Qf=Jr(!0),Jf=Jr("");function nf(){const{subscribe:t,update:e}=Jr([]),i=new Map,r=t=>{e((e=>e.filter((e=>e.id!==t))))};return{subscribe:t,add:(t,n=2e3)=>{if(t.id=t.id??Math.random().toString(),e((e=>{const r=e.find((e=>e.id===t.id));return r?(Object.assign(r,t),i.has(t.id)&&(clearTimeout(i.get(t.id)),i.delete(t.id))):e.push(t),e})),n){const e=setTimeout((()=>{r(t.id),i.delete(t.id)}),n);i.set(t.id,e)}},dismiss:t=>{r(t),i.has(t)&&(clearTimeout(i.get(t)),i.delete[t])}}}const Kf=nf(),rf=(t,e,i)=>{const r=(null==i?void 0:i.serialize)??JSON.stringify,n=(null==i?void 0:i.deserialize)??JSON.parse,s=Jr(n(localStorage.getItem(t))??e),{subscribe:o,set:a}=s,l=e=>{typeof e>"u"||null===e?localStorage.removeItem(t):localStorage.setItem(t,r(e))};return l(ws(s)),{subscribe:o,set:t=>{a(t),l(t)},update:t=>{const e=t(ws(s));a(e),l(e)}}},Zf=rf("showQueries",kl);let sf=(t=21)=>crypto.getRandomValues(new Uint8Array(t)).reduce(((t,e)=>t+((e&=63)<36?e.toString(36):e<62?(e-26).toString(36).toUpperCase():e>62?"-":"_")),"");const of={};var la={};const af=new Set,Xf=t=>{bi()?t():af.add(t)},bi=()=>typeof process<"u"&&!(!la.EVIDENCE_DEBUG&&!la.VITE_PUBLIC_EVIDENCE_DEBUG);class Oi{constructor(t,e){t&&(this.table=String(t)),e&&(this.column=e)}get columns(){return this.column?[this.column]:[]}toString(){const{table:t,column:e}=this;if(e){const i=e.startsWith("*")?e:`"${e}"`;return`${t?`${ua(t)}.`:""}${i}`}return t?ua(t):"NULL"}}function ua(t){return t.split(".").map((t=>`"${t}"`)).join(".")}function cf(t,e){return t instanceof Oi&&t.column===e}function en(t){return"string"==typeof t?uf(t):t}function gs(t){return"string"==typeof t?lf(t):t}function lf(t){return new Oi(t)}function uf(t,e=null){return 1===arguments.length&&(e=t,t=null),new Oi(t,e)}function yo(t){switch(typeof t){case"boolean":return t?"TRUE":"FALSE";case"string":return`'${t.replace("'","''")}'`;case"number":return Number.isFinite(t)?String(t):"NULL";default:if(null==t)return"NULL";if(t instanceof Date){const e=+t;if(Number.isNaN(e))return"NULL";const i=t.getUTCFullYear(),r=t.getUTCMonth(),n=t.getUTCDate();return e===Date.UTC(i,r,n)?`MAKE_DATE(${i}, ${r+1}, ${n})`:`EPOCH_MS(${e})`}return t instanceof RegExp?`'${t.source}'`:String(t)}}const Di=t=>"function"==typeof(null==t?void 0:t.addEventListener);function Tl(t){return t instanceof ns}class ns{constructor(t,e,i){this._expr=Array.isArray(t)?t:[t],this._deps=e||[],this.annotate(i);const r=this._expr.filter((t=>Di(t)));r.length>0?(this._params=Array.from(new Set(r)),this._params.forEach((t=>{t.addEventListener("value",(()=>{var t;return hf(this,null==(t=this.map)?void 0:t.get("value"))}))}))):this.addEventListener=void 0}get value(){return this}get columns(){const{_params:t,_deps:e}=this;if(t){const i=new Set(t.flatMap((t=>{var e;const i=null==(e=t.value)?void 0:e.columns;return Array.isArray(i)?i:[]})));if(i.size){const t=new Set(e);return i.forEach((e=>t.add(e))),Array.from(t)}}return e}get column(){return this._deps.length?this._deps[0]:this.columns[0]}annotate(...t){return Object.assign(this,...t)}toString(){return this._expr.map((t=>Di(t)&&!Tl(t)?yo(t.value):t)).join("")}addEventListener(t,e){const i=this.map||(this.map=new Map);(i.get(t)||(i.set(t,new Set),i.get(t))).add(e)}}function hf(t,e){if(null!=e&&e.size)return Promise.allSettled(Array.from(e,(e=>e(t))))}function Al(t,e){const i=[t[0]],r=new Set,n=e.length;for(let s=0,o=0;s<n;){const n=e[s];Di(n)?i[++o]=n:(Array.isArray(null==n?void 0:n.columns)&&n.columns.forEach((t=>r.add(t))),i[o]+="string"==typeof n?n:yo(n));const a=t[++s];Di(i[o])?i[++o]=a:i[o]+=a}return{spans:i,cols:Array.from(r)}}function Ct(t,...e){const{spans:i,cols:r}=Al(t,e);return new ns(i,r)}function Ls(t,e){return Array.from({length:t},(()=>e))}class fn extends ns{constructor(t,e,i,r,n="",s="",o=""){let a;a=r&&!(n||s||o)?r?Ct`${e} OVER "${r}"`:Ct`${e} OVER ()`:Ct`${e} OVER (${r?`"${r}" `:""}${n}${n&&s?" ":""}${s}${(n||s)&&o?" ":""}${o})`,i&&(a=Ct`(${a})::${i}`);const{_expr:l,_deps:c}=a;super(l,c),this.window=t,this.func=e,this.type=i,this.name=r,this.group=n,this.order=s,this.frame=o}get basis(){return this.column}get label(){const{func:t}=this;return t.label??t.toString()}over(t){const{window:e,func:i,type:r,group:n,order:s,frame:o}=this;return new fn(e,i,r,t,n,s,o)}partitionby(...t){const e=t.flat().filter((t=>t)).map(en),i=Ct(["PARTITION BY ",Ls(e.length-1,", "),""],...e),{window:r,func:n,type:s,name:o,order:a,frame:l}=this;return new fn(r,n,s,o,i,a,l)}orderby(...t){const e=t.flat().filter((t=>t)).map(en),i=Ct(["ORDER BY ",Ls(e.length-1,", "),""],...e),{window:r,func:n,type:s,name:o,group:a,frame:l}=this;return new fn(r,n,s,o,a,i,l)}rows(t){const e=ha("ROWS",t),{window:i,func:r,type:n,name:s,group:o,order:a}=this;return new fn(i,r,n,s,o,a,e)}range(t){const e=ha("RANGE",t),{window:i,func:r,type:n,name:s,group:o,order:a}=this;return new fn(i,r,n,s,o,a,e)}}function ha(t,e){if(Di(e)){const i=Ct`${e}`;return i.toString=()=>`${t} ${da(e.value)}`,i}return`${t} ${da(e)}`}function da(t){const[e,i]=t;return`BETWEEN ${0===e?"CURRENT ROW":Number.isFinite(e)?`${Math.abs(e)} PRECEDING`:"UNBOUNDED PRECEDING"} AND ${0===i?"CURRENT ROW":Number.isFinite(i)?`${Math.abs(i)} FOLLOWING`:"UNBOUNDED FOLLOWING"}`}class wi extends ns{constructor(t,e,i,r,n){e=(e||[]).map(en);const{strings:s,exprs:o}=df(t,e,i,r,n),{spans:a,cols:l}=Al(s,o);super(a,l),this.aggregate=t,this.args=e,this.type=i,this.isDistinct=r,this.filter=n}get basis(){return this.column}get label(){const{aggregate:t,args:e,isDistinct:i}=this,r=i?"DISTINCT"+(e.length?" ":""):"",n=e.length?`(${r}${e.map(ff).join(", ")})`:"";return`${t.toLowerCase()}${n}`}distinct(){const{aggregate:t,args:e,type:i,filter:r}=this;return new wi(t,e,i,!0,r)}where(t){const{aggregate:e,args:i,type:r,isDistinct:n}=this;return new wi(e,i,r,n,t)}window(){const{aggregate:t,args:e,type:i,isDistinct:r}=this,n=new wi(t,e,null,r);return new fn(t,n,i)}partitionby(...t){return this.window().partitionby(...t)}orderby(...t){return this.window().orderby(...t)}rows(t){return this.window().rows(t)}range(t){return this.window().range(t)}}function df(t,e,i,r,n){const s=")"+(i?`::${i}`:"");let o=[`${t}(${r?"DISTINCT ":""}`],a=[];return e.length?(o=o.concat([...Ls(e.length-1,", "),`${s}${n?" FILTER (WHERE ":""}`,...n?[")"]:[]]),a=[...e,...n?[n]:[]]):o[0]+="*"+s,{exprs:a,strings:o}}function ff(t){const e=yo(t);return e&&e.startsWith('"')&&e.endsWith('"')?e.slice(1,-1):e}function ii(t,e){return(...i)=>new wi(t,i,e)}const pf=ii("COUNT","INTEGER"),yf=ii("AVG"),mf=ii("MAX"),gf=ii("MIN"),bf=ii("SUM","DOUBLE"),_f=ii("MEDIAN");let ur=class t{static select(...e){return(new t).select(...e)}static from(...e){return(new t).from(...e)}static with(...e){return(new t).with(...e)}static union(...t){return new yn("UNION",t.flat())}static unionAll(...t){return new yn("UNION ALL",t.flat())}static intersect(...t){return new yn("INTERSECT",t.flat())}static except(...t){return new yn("EXCEPT",t.flat())}static describe(e){const i=e.clone(),{clone:r,toString:n}=i;return Object.assign(i,{describe:!0,clone:()=>t.describe(r.call(i)),toString:()=>`DESCRIBE ${n.call(i)}`})}constructor(){this.query={with:[],select:[],from:[],where:[],groupby:[],having:[],window:[],qualify:[],orderby:[]},this.cteFor=null}clone(){const e=new t;return e.query={...this.query},e}with(...t){const{query:e}=this;if(0===t.length)return e.with;{const i=[],r=(t,e)=>{const r=e.clone();r.cteFor=this,i.push({as:t,query:r})};return t.flat().forEach((t=>{if(null!=t)if(t.as&&t.query)r(t.as,t.query);else for(const e in t)r(e,t[e])})),e.with=e.with.concat(i),this}}select(...t){const{query:e}=this;if(0===t.length)return e.select;{const i=[];for(const e of t.flat())if(null!=e)if("string"==typeof e)i.push({as:e,expr:en(e)});else if(e instanceof Oi)i.push({as:e.column,expr:e});else if(Array.isArray(e))i.push({as:e[0],expr:e[1]});else for(const t in e)i.push({as:Zi(t),expr:en(e[t])});const r=new Set(i.map((t=>t.as)));return e.select=e.select.filter((t=>!r.has(t.as))).concat(i.filter((t=>t.expr))),this}}$select(...t){return this.query.select=[],this.select(...t)}distinct(t=!0){return this.query.distinct=!!t,this}from(...t){const{query:e}=this;if(0===t.length)return e.from;{const i=[];return t.flat().forEach((t=>{if(null!=t)if("string"==typeof t)i.push({as:t,from:gs(t)});else if(t instanceof Oi)i.push({as:t.table,from:t});else if(bs(t)||Tl(t))i.push({from:t});else if(Array.isArray(t))i.push({as:Zi(t[0]),from:gs(t[1])});else for(const e in t)i.push({as:Zi(e),from:gs(t[e])})})),e.from=e.from.concat(i),this}}$from(...t){return this.query.from=[],this.from(...t)}sample(t,e){const{query:i}=this;if(0===arguments.length)return i.sample;{let r=t;return"number"==typeof t&&(r=t>0&&t<1?{perc:100*t,method:e}:{rows:Math.round(t),method:e}),i.sample=r,this}}where(...t){const{query:e}=this;return 0===t.length?e.where:(e.where=e.where.concat(t.flat().filter((t=>t))),this)}$where(...t){return this.query.where=[],this.where(...t)}groupby(...t){const{query:e}=this;return 0===t.length?e.groupby:(e.groupby=e.groupby.concat(t.flat().filter((t=>t)).map(en)),this)}$groupby(...t){return this.query.groupby=[],this.groupby(...t)}having(...t){const{query:e}=this;return 0===t.length?e.having:(e.having=e.having.concat(t.flat().filter((t=>t))),this)}window(...t){const{query:e}=this;if(0===t.length)return e.window;{const i=[];return t.flat().forEach((t=>{if(null!=t)for(const e in t)i.push({as:Zi(e),expr:t[e]})})),e.window=e.window.concat(i),this}}qualify(...t){const{query:e}=this;return 0===t.length?e.qualify:(e.qualify=e.qualify.concat(t.flat().filter((t=>t))),this)}orderby(...t){const{query:e}=this;return 0===t.length?e.orderby:(e.orderby=e.orderby.concat(t.flat().filter((t=>t)).map(en)),this)}limit(t){const{query:e}=this;return 0===arguments.length?e.limit:(e.limit=Number.isFinite(t)?t:void 0,this)}offset(t){const{query:e}=this;return 0===arguments.length?e.offset:(e.offset=Number.isFinite(t)?t:void 0,this)}get subqueries(){const{query:t,cteFor:e}=this,i=((null==e?void 0:e.query)||t).with,r=null==i?void 0:i.reduce(((t,{as:e,query:i})=>(t[e]=i,t)),{}),n=[];return t.from.forEach((({from:t})=>{if(bs(t))n.push(t);else if(r[t.table]){const e=r[t.table];n.push(e)}})),n}toString(){const{with:t,select:e,distinct:i,from:r,sample:n,where:s,groupby:o,having:a,window:l,qualify:c,orderby:u,limit:h,offset:d}=this.query,f=[];if(t.length){const e=t.map((({as:t,query:e})=>`"${t}" AS (${e})`));f.push(`WITH ${e.join(", ")}`)}const p=e.map((({as:t,expr:e})=>cf(e,t)&&!e.table?`${e}`:`${e} AS "${t}"`));if(f.push(`SELECT${i?" DISTINCT":""} ${p.join(", ")}`),r.length){const t=r.map((({as:t,from:e})=>{const i=bs(e)?`(${e})`:`${e}`;return t&&t!==e.table?`${i} AS "${t}"`:i}));f.push(`FROM ${t.join(", ")}`)}if(s.length){const t=s.map(String).filter((t=>t)).join(" AND ");t&&f.push(`WHERE ${t}`)}if(n){const{rows:t,perc:e,method:i,seed:r}=n,s=t?`${t} ROWS`:`${e} PERCENT`,o=i?` (${i}${null!=r?`, ${r}`:""})`:"";f.push(`USING SAMPLE ${s}${o}`)}if(o.length&&f.push(`GROUP BY ${o.join(", ")}`),a.length){const t=a.map(String).filter((t=>t)).join(" AND ");t&&f.push(`HAVING ${t}`)}if(l.length){const t=l.map((({as:t,expr:e})=>`"${t}" AS (${e})`));f.push(`WINDOW ${t.join(", ")}`)}if(c.length){const t=c.map(String).filter((t=>t)).join(" AND ");t&&f.push(`QUALIFY ${t}`)}return u.length&&f.push(`ORDER BY ${u.join(", ")}`),Number.isFinite(h)&&f.push(`LIMIT ${h}`),Number.isFinite(d)&&f.push(`OFFSET ${d}`),f.join(" ")}};class yn{constructor(t,e){this.op=t,this.queries=e.map((t=>t.clone())),this.query={orderby:[]},this.cteFor=null}clone(){const t=new yn(this.op,this.queries);return t.query={...this.query},t}orderby(...t){const{query:e}=this;return 0===t.length?e.orderby:(e.orderby=e.orderby.concat(t.flat().filter((t=>t)).map(en)),this)}limit(t){const{query:e}=this;return 0===arguments.length?e.limit:(e.limit=Number.isFinite(t)?t:void 0,this)}offset(t){const{query:e}=this;return 0===arguments.length?e.offset:(e.offset=Number.isFinite(t)?t:void 0,this)}get subqueries(){const{queries:t,cteFor:e}=this;return e&&t.forEach((t=>t.cteFor=e)),t}toString(){const{op:t,queries:e,query:{orderby:i,limit:r,offset:n}}=this,s=[e.join(` ${t} `)];return i.length&&s.push(`ORDER BY ${i.join(", ")}`),Number.isFinite(r)&&s.push(`LIMIT ${r}`),Number.isFinite(n)&&s.push(`OFFSET ${n}`),s.join(" ")}}function bs(t){return t instanceof ur||t instanceof yn}function Zi(t){return wf(t)?t.slice(1,-1):t}function wf(t){return'"'===t[0]&&'"'===t[t.length-1]}const _s=t=>{let e=null,i=null;const r=new Promise(((t,r)=>{e=t,i=r}));let n="init",s=null;if(!e||!i)throw new Error;return{promise:r,resolve:i=>{if(!e)throw new Error("SharedPromise encountered an error: res not defined");("loading"===n||"init"===n)&&(n="resolved",s=i,e(i),null==t||t())},reject:e=>{if(!i)throw new Error("SharedPromise encountered an error: rej not defined");("loading"===n||"init"===n)&&(n="rejected",r.catch((()=>{})),i(e),null==t||t())},get state(){return n},get value(){return s},start(){n="loading",null==t||t()}}},_e=(t,e,i)=>{try{const r="function"==typeof e?e():e;return r instanceof Promise?r.then((e=>t(e,!0))).catch((t=>{const e=t instanceof Error?t:new Error("Unknown Error",{cause:t});if(i)return i(e,!0);throw e})):t(r,!1)}catch(t){const e=t instanceof Error?t:new Error("Unknown Error",{cause:t});if(i)return i(e,!1);throw e}},fa=(t,e)=>{const i=e.reduce(((t,e)=>t+Sf(e.column_type)),4*e.length);return Math.abs(i*t)};function If(t){return t.startsWith("STRUCT")||t.endsWith("[]")}function vf(t){return t.startsWith("DECIMAL")}function Sf(t){if(If(t))return console.warn("[!] Evidence does not support DuckDB Struct or Array\nIf you need to use one, convert it to JSON in your query, and then manually parse it in your project"),30;if(vf(t))return 12;switch(t){case"BOOLEAN":return 4;case"BIGINT":case"DOUBLE":case"FLOAT":case"INTEGER":case"SMALLINT":case"TINYINT":case"UBIGINT":case"UINTEGER":case"USMALLINT":case"UTINYINT":case"HUGEINT":return 12;case"UUID":case"VARCHAR":case"INTERVAL":case"TIME":case"TIME WITH TIME ZONE":case"BLOB":case"BIT":return 30;case"DATE":case"TIMESTAMP":case"TIMESTAMP_S":case"TIMESTAMP_MS":case"TIMESTAMP_NS":case"TIMESTAMP WITH TIME ZONE":return 48;default:return console.error(`Column type ${t} is not supported`),30}}const Ef=/--([^']|'.*')+$/,Tf=/(\/\*.*\*\/)/g,Af=t=>{const e=t.split("\n");let i=!1;for(let t=e.length;t>0;t--){let r=e[t-1],n="";const s=Array.from(r.matchAll(Tf));for(const t of s){const e=r.slice(0,t.index),i=r.slice(t.index+t[0].length);r=`${e}${i}`}if(i&&r.includes("/*")){i=!1;const t=r.split("/*");r=t.slice(0,-1).join("/*"),n+="/*"+t.slice(-1)}if(r.trim().endsWith("*/")){i=!0;continue}const o=Ef.exec(r);if(o){const t=r.slice(0,o.index),e=t.trimEnd();if(e.endsWith(";")){const i=r.slice(o.index),n=t.slice(e.length,t.length);r=`${t.slice(0,-1*(t.length-e.length)-1)}${n}${i}`}}else if(r.trimEnd().endsWith(";")){const t=r.lastIndexOf(";");r=r.slice(0,t)+r.slice(t+1)}for(const t of s){const e=r.slice(0,t.index),i=r.slice(t.index);r=`${e}${t[0]}${i}`}if(r!==e[t-1]){e[t-1]=r+n;break}}return e.push(""),e.join("\n")};var gn,Lt,Bi,Ze,Ri,$t,Mi,Ci,Li,bt,He,dn,jt,zn,xe,zr,Wn,bn,Ui,ie,Wr,vt,_n,St,wn,Nt,Yn,Yr,re,Gr,qr,xi,xs,Ps,Pe,ks,Gt,Gn,qn,fe,ke,Xe,Qe,Ut,Pi,Hn,Hr,Qn,tn,ki,Qr;const A=class t{constructor(e,i,r={}){var n;j(this,bt),j(this,gn),j(this,Lt,[]),j(this,Bi,-1),j(this,Ze,0),j(this,Ri,-1),j(this,$t,[]),j(this,Mi),j(this,Ci,-1),j(this,Li),j(this,jt),j(this,zn),j(this,ie,-1),j(this,Wr,(()=>{this.lengthLoaded&&this.columnsLoaded?(nt(this,ie,fa(this.length,this.columns)),u(this,ie)>u(t,Ui)&&u(this,tn).call(this,"highScore",u(this,ie))):Promise.allSettled([u(this,St).promise,u(this,Nt).promise]).then((([e,i])=>{"rejected"!==e.status&&"rejected"!==i.status&&u(this,Ze)&&u(this,$t)?(nt(this,ie,fa(this.length,this.columns)),u(this,ie)>u(t,Ui)&&u(this,tn).call(this,"highScore",u(this,ie))):nt(this,ie,-1)})).catch((t=>{console.error(`${this.id} | Failed to calculate Query score ${t}`)}))})),j(this,vt,_s((()=>this.publish(`data promise (${u(this,vt).state})`)))),j(this,_n,(()=>{var e;if("init"!==u(this,vt).state)return u(this,vt).promise;if(u(this,bt,He))return u(this,Gt).call(this,"data error","Refusing to execute data query, store has an error state"),u(this,vt).promise;if("init"!==u(this,vt).state||this.opts.noResolve)return u(this,vt).promise;u(this,vt).start();const i=`\n---- Data ${u(this,fe)} ${u(this,ke)}\n${this.text.trim()}\n        `.trim()+"\n";u(this,Gn).call(this,"data query text","\n"+i,"font-family: monospace;");const r=u(this,Ut);u(e=t,zr).call(e,this);const n=performance.now();return _e(((t,e)=>{nt(this,Lt,t);const i=performance.now();return n-i>5e3&&(u(this,tn).call(this,"longRun",n-i),u(this,Gt).call(this,"long-running",`Query took ${n-i}ms to execute`)),nt(this,Bi,i-n),u(this,wn).call(this),u(this,vt).resolve(this),u(this,tn).call(this,"dataReady",void 0),e?u(this,vt).promise:this}),(()=>r(i,`${u(this,fe)}_data`)),((t,e)=>(nt(this,bt,t,dn),u(this,vt).reject(t),e?u(this,vt).promise:this)))})),It(this,"fetch",(()=>u(this,Yn).call(this)instanceof Promise&&!this.opts.noResolve?Promise.allSettled([u(this,Yn).call(this),u(this,_n).call(this)]).then((()=>this.value)):(u(this,_n).call(this),this.value))),It(this,"backgroundFetch",(()=>{typeof window>"u"?u(this,Gt).call(this,"background fetch skip","Did not execute backgroundFetch in SSR"):(u(this,Gt).call(this,"background fetch","Executed backgroundFetch"),_e((()=>{}),(async()=>(await new Promise((t=>setTimeout(t,0))),u(this,Ut).call(this,`--data\n${this.text.trim()}`,this.id))),(()=>{})))})),j(this,St,_s((()=>this.publish(`length promise (${u(this,St).state})`)))),j(this,wn,(()=>{if(u(this,Lt)&&"resolved"===u(this,vt).state&&"init"===u(this,St).state)return u(this,Gt).call(this,"length inferred","Inferred length from already-resolved data promise",u(this,Lt)),nt(this,Ze,u(this,Lt).length),u(this,St).resolve(this),u(this,St).promise;if(u(this,bt,He))return u(this,Gt).call(this,"length error","Refusing to execute length query, store has an error state",u(this,bt,He)),u(this,St).reject(u(this,bt,He)),u(this,St).value??u(this,St).promise;if("init"!==u(this,St).state||this.opts.noResolve)return u(this,St).promise;u(this,St).start();const t=`\n---- Length ${u(this,fe)} (${u(this,ke)})\nSELECT COUNT(*) as rowCount FROM (${this.text.trim()})\n        `.trim()+"\n",e=u(this,Ut);u(this,Gn).call(this,"length query text","\n"+t,"font-family: monospace;");const i=performance.now();return _e(((t,e)=>{const r=performance.now();return nt(this,Ri,r-i),nt(this,Ze,t[0].rowCount),u(this,St).resolve(this),e?u(this,St).promise:this}),(()=>e(t,`${u(this,fe)}_length`)),((t,e)=>(nt(this,bt,t,dn),u(this,St).reject(t),e?u(this,St).promise:this)))})),j(this,Nt,_s((()=>this.publish(`columns promise (${u(this,Nt).state})`)))),j(this,Yn,(()=>{if(u(this,bt,He))return u(this,Gt).call(this,"cols query error","Refusing to execute columns query, store has an error state",u(this,bt,He)),u(this,Nt).value??u(this,Nt).promise;if("init"!==u(this,Nt).state||this.opts.noResolve)return u(this,Nt).promise;u(this,Nt).start();const t=`\n---- Columns ${u(this,fe)} (${u(this,ke)})\nDESCRIBE ${this.text.trim()}\n        `.trim()+"\n";u(this,Gn).call(this,"columns query text","\n"+t,"font-family: monospace;");const e=u(this,Ut),i=performance.now();return _e(((t,e)=>{const r=performance.now();return nt(this,Ci,r-i),nt(this,$t,t),u(this,Nt).resolve(this),nt(this,Mi,Object.fromEntries(t.map((t=>[t.column_name,void 0])))),e?u(this,Nt).promise:this}),(()=>e(t,`${u(this,fe)}_columns`)),((t,e)=>(nt(this,bt,t,dn),u(this,Nt).reject(t),e?u(this,Nt).promise:this)))})),j(this,Yr,(()=>new Proxy([],{getPrototypeOf:()=>Object.getPrototypeOf(u(this,Lt)),has:(t,e)=>e in u(this,Lt)||e in this,get:(e,i)=>{let r=i;if("string"==typeof r&&/^[\d.]+$/.exec(r)&&(r=parseInt(r)),("number"==typeof r||t.ProxyFetchTriggers.includes(r.toString()))&&"init"===u(this,vt).state&&(u(this,Gt).call(this,"implicit fetch",`Implicit query fetch triggered by ${r.toString()}`),u(this,_n).call(this)),"length"===r&&u(this,wn).call(this),"constructor"===r)return u(this,Lt).constructor;if("toString"===r)return u(this,Lt).toString.bind(u(this,Lt));const n=r in this?this:u(this,Lt)&&r in u(this,Lt)?u(this,Lt):null;if(null===n)return"number"!=typeof r||this.lengthLoaded&&r>u(this,Ze)?void 0:u(this,Mi)??{};const s=n[r];return"function"==typeof s?s.bind(n):s}}))),j(this,Gt,bi()?(t,...e)=>{const i=`${(performance.now()/1e3).toFixed(3)} | ${this.id} (${this.hash}) | ${t}`;console.groupCollapsed(i);for(const t of e)console.debug("function"==typeof t?t():t);console.groupEnd()}:()=>{}),j(this,Gn,bi()?(t,e,i)=>{const r=`${(performance.now()/1e3).toFixed(3)} | ${this.id} (${this.hash}) | ${t}`;console.groupCollapsed(r),console.debug(`%c${e}`,i),console.groupEnd()}:()=>{}),j(this,fe),j(this,ke),j(this,Xe),j(this,Ut),It(this,"opts"),j(this,Pi),j(this,Hn,new Set),It(this,"subscribe",(t=>(u(this,Hn).add(t),t(u(this,gn)),()=>u(this,Hn).delete(t)))),j(this,Hr,0),It(this,"publish",(t=>{if(Ro(this,Hr)._++>1e5)throw new Error("Query published too many times.");u(this,Gt).call(this,"publish",`Publishing triggered by ${t}`),u(this,Hn).forEach((t=>t(u(this,gn))))})),j(this,Qn,{dataReady:new Set,error:new Set,highScore:new Set,longRun:new Set}),j(this,tn,((t,e)=>{u(this,Qn)[t].forEach((i=>i(e,t)))})),It(this,"on",((t,e)=>{u(this,Qn)[t].add(e)})),It(this,"off",((t,e)=>{u(this,Qn)[t].delete(e)})),It(this,"addEventListener",this.on),It(this,"removeEventListener",this.off),It(this,"where",(e=>t.create(u(this,jt).clone().where(Ct`${e}`),u(this,Ut),{knownColumns:u(this,$t),noResolve:u(this,Xe).noResolve}))),It(this,"withOrdinal",(e=>{const i=u(this,jt).clone();return i.select({ordinal:Ct`row_number() over (${e})`}),t.create(i,u(this,Ut),{...u(this,bt,Qe),knownColumns:u(this,$t)})})),It(this,"search",((e,i,r)=>{(typeof r>"u"||r<0||r>1)&&(r=1-1/e.length);const n=[...u(this,$t),{column_name:"similarity",column_type:"INTEGER",nullable:"NO"}],s=t.create,o=e.replaceAll("'","''"),a=(Array.isArray(i)?i:[i]).map((t=>Ct`GREATEST((${Ct`CASE WHEN lower("${t.trim()}") = lower('${o}') THEN 2 ELSE 0 END`}), (${Ct`jaccard(lower('${o}'), lower("${t}"))`}), (${o.length>=1?Ct`CASE WHEN lower("${t.trim()}") LIKE lower('%${o.split(" ").join("%")}%') THEN 1 ELSE 0 END`:Ct`0`}))`)).join(",");return s(u(this,jt).clone().$select({similarity:Ct`GREATEST(${a})`},"*").where(Ct`"similarity" > ${r} `).orderby(Ct`"similarity" DESC`),u(this,Ut),{knownColumns:n,...u(this,bt,Qe)})})),It(this,"limit",(e=>t.create(u(this,jt).clone().limit(e),u(this,Ut),{knownColumns:u(this,$t),...u(this,bt,Qe)}))),It(this,"offset",(e=>t.create(u(this,jt).clone().offset(e),u(this,Ut),{knownColumns:u(this,$t),...u(this,bt,Qe)}))),It(this,"paginate",((e,i)=>t.create(u(this,jt).clone().offset(e).limit(i),u(this,Ut),{knownColumns:u(this,$t),...u(this,bt,Qe)}))),It(this,"groupBy",((e,i)=>{const r=u(this,jt).clone();return r.$select(e),i&&r.select({rows:pf("*")}),r.$groupby(e),t.create(r,u(this,Ut),{knownColumns:u(this,$t),...u(this,bt,Qe)})})),It(this,"agg",(e=>{var i;const r=u(this,jt).clone();for(const[n,s]of Object.entries(e)){if(!u(i=t,Qr).call(i,n))throw new Error(`Unknown agg function: ${n}`);const e=u(t,ki)[n],o=Array.isArray(s)?s:[s];for(const t of o){const i="object"==typeof t?t.as:`${n}_${t}`,s="object"==typeof t?t.col:t;r.select({[i]:e(s)})}}return t.create(r,u(this,Ut),{knownColumns:u(this,$t),...u(this,bt,Qe)})})),nt(this,Pi,null==(n=(new Error).stack)?void 0:n.split("\n").slice(2).map((t=>t.slice(7))).join("\n"));const{id:s,initialData:o,knownColumns:a,initialError:l}=r;if(this.opts=r,nt(this,Ut,i),"string"!=typeof e&&!(e instanceof ur)&&(console.warn(`Query ${s} has no query text`),r.noResolve=!0),u(t,qn)||console.warn("Directly using new Query() is not a recommended use-case. Please use Query.create()"),nt(t,qn,!1),nt(this,gn,u(this,Yr).call(this)),nt(this,zn,(null==e?void 0:e.toString())??"SELECT 'Empty Query' WHERE 0"),nt(this,ke,hn(u(this,zn))),nt(this,fe,s??u(this,ke)),nt(this,Xe,r),e&&"string"!=typeof e)nt(this,jt,e);else{if(!e)return nt(this,jt,new ur),void nt(this,bt,new Error("Refusing to create Query: No Query Text provided"),dn);{const t=(new ur).from({[`inputQuery-${sf(2)}`]:Ct`(${Af(e)})`}).select("*");nt(this,jt,t)}}if(l)nt(this,bt,l,dn);else{if(o)u(this,Gt).call(this,"initial data","Created with initial data",o),_e((t=>{nt(this,Lt,t),r.initialDataDirty?(this.publish("dataDirty"),u(this,_n).call(this)):(u(this,vt).resolve(this),u(this,wn).call(this))}),o,(t=>{nt(this,bt,t,dn)}));else if(r.noResolve)return u(this,vt).start(),u(this,St).start(),u(this,Nt).start(),this;if(a){if(!Array.isArray(a))throw new Error("Expected knownColumns to be an array",{cause:a});u(this,Gt).call(this,"known columns","Created with known columns",a),nt(this,$t,a),u(this,Nt).resolve(this)}else _e((()=>{}),u(this,Yn).call(this),((t,e)=>{if(!e)throw t}));_e((()=>{}),u(this,wn).call(this),((t,e)=>{if(!e)throw t})),r.autoScore&&u(this,Wr).call(this)}}get value(){return u(this,gn)}get dataLoaded(){return["resolved","rejected"].includes(u(this,vt).state)}get dataLoading(){return"loading"===u(this,vt).state}get dataQueryTime(){return u(this,Bi)}get length(){return u(this,Ze)}get lengthLoaded(){return["resolved","rejected"].includes(u(this,St).state)}get lengthLoading(){return"loading"===u(this,St).state}get lengthQueryTime(){return u(this,Ri)}get columns(){return u(this,$t)}get columnsLoaded(){return["resolved","rejected"].includes(u(this,Nt).state)}get columnsLoading(){return"loading"===u(this,Nt).state}get columnsQueryTime(){return u(this,Ci)}get ready(){return"resolved"===u(this,St).state&&"resolved"===u(this,Nt).state&&"resolved"===u(this,vt).state}get loading(){return"loading"===u(this,St).state||"loading"===u(this,Nt).state||"loading"===u(this,vt).state}get error(){return u(this,bt,He)}get originalText(){return u(this,zn)}get text(){var t;return(null==(t=u(this,jt))?void 0:t.toString())??"SELECT 'Empty Query' WHERE 0"}static get queriesInFlight(){return u(t,xe).size>0}static resetInFlightQueries(){nt(t,xe,new Set)}static addEventListener(t,e){u(this,Wn)[t].add(e)}static removeEventListener(t,e){u(this,Wn)[t].delete(e)}get score(){return u(this,ie)}get isQuery(){return!0}static[Symbol.hasInstance](e){return t.isQuery(e)}static get ProxyFetchTriggers(){return["at"]}static get cacheSize(){return u(this,re).size}get id(){return u(this,fe)}get hash(){return u(this,ke)}get createdStack(){return u(this,Pi)}};gn=new WeakMap,Lt=new WeakMap,Bi=new WeakMap,Ze=new WeakMap,Ri=new WeakMap,$t=new WeakMap,Mi=new WeakMap,Ci=new WeakMap,Li=new WeakMap,bt=new WeakSet,He=function(){return u(this,Li)},dn=function(t){t&&(console.error(`${this.id} | Error in Query!`,null==t?void 0:t.message),u(this,tn).call(this,"error",t),nt(this,Li,t))},jt=new WeakMap,zn=new WeakMap,xe=new WeakMap,zr=new WeakMap,Wn=new WeakMap,bn=new WeakMap,Ui=new WeakMap,ie=new WeakMap,Wr=new WeakMap,vt=new WeakMap,_n=new WeakMap,St=new WeakMap,wn=new WeakMap,Nt=new WeakMap,Yn=new WeakMap,Yr=new WeakMap,re=new WeakMap,Gr=new WeakMap,qr=new WeakMap,xi=new WeakMap,xs=new WeakMap,Ps=new WeakMap,Pe=new WeakMap,ks=new WeakMap,Gt=new WeakMap,Gn=new WeakMap,qn=new WeakMap,fe=new WeakMap,ke=new WeakMap,Xe=new WeakMap,Qe=function(){return{autoScore:u(this,Xe).autoScore,noResolve:u(this,Xe).noResolve,disableCache:u(this,Xe).disableCache}},Ut=new WeakMap,Pi=new WeakMap,Hn=new WeakMap,Hr=new WeakMap,Qn=new WeakMap,tn=new WeakMap,ki=new WeakMap,Qr=new WeakMap,j(A,xe,new Set),j(A,zr,(t=>{var e;0===u(A,xe).size&&u(e=A,bn).call(e,"inFlightQueryStart",void 0),u(A,xe).add(t),u(t,vt).promise.finally((()=>{var e;u(A,xe).delete(t),0===u(A,xe).size&&u(e=A,bn).call(e,"inFlightQueryEnd",void 0)}))})),j(A,Wn,{inFlightQueryStart:new Set,inFlightQueryEnd:new Set,queryCreated:new Set,cacheCleared:new Set}),j(A,bn,((t,e)=>{u(A,Wn)[t].forEach((i=>i(e,t)))})),j(A,Ui,10485760),It(A,"isQuery",(t=>!("object"!=typeof t||!t)&&"isQuery"in t&&!0===t.isQuery)),It(A,"CacheMaxScore",51200),j(A,re,new Map),It(A,"emptyCache",(()=>{var t;u(A,re).clear(),u(t=A,bn).call(t,"cacheCleared",void 0)})),j(A,Gr,(t=>{var e;u(A,re).set(t.hash,{query:t,added:Date.now()}),u(e=A,Pe).call(e,"cache",`Added to cache: ${t.hash}`,{cacheSize:u(A,re).size,cacheScore:Array.from(u(A,re).values()).reduce(((t,e)=>t+e.query.score),0)})})),j(A,qr,(t=>{const e=u(A,re).get(t);return e?e.query:null})),j(A,xi,(()=>{let t=Array.from(u(A,re).values()).reduce(((t,e)=>t+e.query.score),0);const e=Array.from(u(A,re).values()).sort(((t,e)=>t.added-e.added));for(;t>A.CacheMaxScore;){const i=e.shift();if(!i)break;u(A,re).delete(i.query.hash),t-=i.query.score}})),It(A,"createReactive",((t,e,i)=>{const{loadGracePeriod:r=250,callback:n=()=>{},execFn:s}=t,o=A.create;let a,l=i,c=0;function h(){e={...e,initialData:void 0,initialError:void 0}}return(t,i)=>{if(l)return void _e((()=>{}),((t,i)=>{var h;if(!l)throw new Error;c+=1;const d=c;u(h=A,Pe).call(h,`${l.id} (${hn(t)}) | Reactive Updating`,t,{changeIdx:c,targetChangeIdx:d,hash:hn(t)},{initialOpts:e,newOpts:i});const f=A.isQuery(t)?t:o(t,s,Object.assign({},e,{initialData:void 0,initialError:void 0},i)),p=f.fetch();let y=p;p instanceof Promise&&(y=Promise.race([new Promise((t=>setTimeout(t,r))),f.fetch()])),_e((()=>{var t;c===d?(null==a||a(),l=f.value,a=l.subscribe(n)):u(t=A,Pe).call(t,"changeIdx does not match, results are discarded")}),y,(t=>{throw console.warn(`Error while attempting to update reactive query: ${t.message}`),t}))})(t,i),(t=>{console.warn(`Error while attempting to update reactive query: ${t.message}`)}));l=o(t,s,Object.assign({},e,i));const d=l.fetch();_e(h,d),a=l.subscribe(n),n(l)}})),j(A,xs,!1),j(A,Ps,(()=>{})),It(A,"create",((t,e,i,r)=>{var n,s,o,a,l,c,h,d;const f=hn(t);let p;if("string"==typeof i?p={...r,id:i}:i?(p=i,p.id||(p.id=f+"-"+Math.random().toString(36).substring(0,4))):p={id:f+"-"+Math.random().toString(36).substring(0,4)},"autoScore"in p||(p.autoScore=!0),p.disableCache)u(l=A,Pe).call(l,`${p.id??"[query id missing]"} (${f}) | cache disabled`,`Cache is disabled for ${p.id??"[query id missing]"}`,{opts:p,query:t,hash:hn(t)});else{const e=u(n=A,qr).call(n,f);if(u(s=A,xi).call(s),e)return u(o=A,Pe).call(o,`${p.id??"[query id missing]"} (${f}) | Using cached query`,{opts:p,hash:hn(t)},t,e),e.value;u(a=A,Pe).call(a,`${p.id??"[query id missing]"} (${f}) | Cached query not found`,{opts:p,hash:hn(t)},t)}nt(A,qn,!0);const y=new A(t,e,p);return u(c=A,bn).call(c,"queryCreated",{raw:y,proxied:y.value}),p.disableCache||(u(h=A,Gr).call(h,y),u(d=A,xi).call(d)),y.value})),j(A,Pe,bi()?(t,...e)=>{const i=`${(performance.now()/1e3).toFixed(3)} | Query | ${t}`;console.groupCollapsed(i);for(const t of e)console.debug("function"==typeof t?t():t);console.groupEnd()}:()=>{}),j(A,ks,bi()?(t,e,i)=>{const r=`${(performance.now()/1e3).toFixed(3)} | Query | ${t}`;console.groupCollapsed(r),console.debug(`%c${e}`,i),console.groupEnd()}:()=>{}),j(A,qn,!1),j(A,ki,{sum:bf,avg:yf,min:gf,max:mf,median:_f}),j(A,Qr,(t=>t in u(A,ki)));let Us=A;const hn=(...t)=>(t=>{let e=0;for(let i=0;i<t.length;i++)e=(e<<5)-e+t.charCodeAt(i),e&=e;return new Uint32Array([e])[0].toString(36)})(JSON.stringify(t)),Ii=Symbol("InputStore"),Fl=t=>"object"==typeof t&&null!==t&&"subscribe"in t,Ff=t=>!!Fl(t)&&"set"in t&&"update"in t,tp=t=>{if(!Ff(t))throw console.error({InputStoreValue:t}),new Error("InputStore must be a writable store");if(pa().has(Ii)){const e=ma(Ii);return e.set(ws(t)),e}return ya(Ii,t),t},ep=()=>{if(!pa().has(Ii))return console.warn("InputStoreKey not found in context. Did you forget to call ensureInputContext?"),$l({});const t=ma(Ii);if(Fl(t))return jl(t);throw new Error(`InputStoreKey is not a readable store: ${t}`)},Of=(t,e)=>{let i;if(t instanceof String)i=t.toString();else{if("string"!=typeof t)return t;i=t}if(i.startsWith("http")||i.startsWith("#")||/^[^/]*:/.test(i))return i;let r=e.deployment.basePath;return r?(null!=r&&r.startsWith("/")||(r=`/${r}`),r.endsWith("/")&&(r=r.slice(0,-1)),i.startsWith(r)?i:(i.startsWith("/")||(i=`/${i}`),`${r}${i}`)):i},Df={deployment:{basePath:""}},np=t=>Of(t,Df);var ip=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Nf(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}const Bf="___usql_query";let Ol=ef;const rp=t=>{ya(Bf,t),Ol=t},sp=(t,e,i,r)=>Us.create(t,Ol,e,{...r,initialData:i});var Dl={exports:{}};!function(t){var e={};(function(t){function e(t){for(var e="",i=t.length-1;i>=0;)e+=t.charAt(i--);return e}function i(t,e){for(var i="";i.length<e;)i+=t;return i}function r(t,e){var r=""+t;return r.length>=e?r:i("0",e-r.length)+r}function n(t,e){var r=""+t;return r.length>=e?r:i(" ",e-r.length)+r}function s(t,e){var r=""+t;return r.length>=e?r:r+i(" ",e-r.length)}t.version="0.11.2";var o=Math.pow(2,32);function a(t,e){return t>o||t<-o?function(t,e){var r=""+Math.round(t);return r.length>=e?r:i("0",e-r.length)+r}(t,e):function(t,e){var r=""+t;return r.length>=e?r:i("0",e-r.length)+r}(Math.round(t),e)}function l(t,e){return e=e||0,t.length>=7+e&&103==(32|t.charCodeAt(e))&&101==(32|t.charCodeAt(e+1))&&110==(32|t.charCodeAt(e+2))&&101==(32|t.charCodeAt(e+3))&&114==(32|t.charCodeAt(e+4))&&97==(32|t.charCodeAt(e+5))&&108==(32|t.charCodeAt(e+6))}var c=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],u=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function h(t){t[0]="General",t[1]="0",t[2]="0.00",t[3]="#,##0",t[4]="#,##0.00",t[9]="0%",t[10]="0.00%",t[11]="0.00E+00",t[12]="# ?/?",t[13]="# ??/??",t[14]="m/d/yy",t[15]="d-mmm-yy",t[16]="d-mmm",t[17]="mmm-yy",t[18]="h:mm AM/PM",t[19]="h:mm:ss AM/PM",t[20]="h:mm",t[21]="h:mm:ss",t[22]="m/d/yy h:mm",t[37]="#,##0 ;(#,##0)",t[38]="#,##0 ;[Red](#,##0)",t[39]="#,##0.00;(#,##0.00)",t[40]="#,##0.00;[Red](#,##0.00)",t[45]="mm:ss",t[46]="[h]:mm:ss",t[47]="mmss.0",t[48]="##0.0E+0",t[49]="@",t[56]='"上午/下午 "hh"時"mm"分"ss"秒 "'}var d={};h(d);var f=[],p=0;for(p=5;p<=8;++p)f[p]=32+p;for(p=23;p<=26;++p)f[p]=0;for(p=27;p<=31;++p)f[p]=14;for(p=50;p<=58;++p)f[p]=14;for(p=59;p<=62;++p)f[p]=p-58;for(p=67;p<=68;++p)f[p]=p-58;for(p=72;p<=75;++p)f[p]=p-58;for(p=67;p<=68;++p)f[p]=p-57;for(p=76;p<=78;++p)f[p]=p-56;for(p=79;p<=81;++p)f[p]=p-34;var y=[];function m(t,e,i){for(var r=t<0?-1:1,n=t*r,s=0,o=1,a=0,l=1,c=0,u=0,h=Math.floor(n);c<e&&(a=(h=Math.floor(n))*o+s,u=h*c+l,!(n-h<5e-8));)n=1/(n-h),s=o,o=a,l=c,c=u;if(u>e&&(c>e?(u=l,a=s):(u=c,a=o)),!i)return[0,r*a,u];var d=Math.floor(r*a/u);return[d,r*a-d*u,u]}function b(t,e,i){if(t>2958465||t<0)return null;var r=0|t,n=Math.floor(86400*(t-r)),s=0,o=[],a={D:r,T:n,u:86400*(t-r)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(a.u)<1e-6&&(a.u=0),e&&e.date1904&&(r+=1462),a.u>.9999&&(a.u=0,86400==++n&&(a.T=n=0,++r,++a.D)),60===r)o=i?[1317,10,29]:[1900,2,29],s=3;else if(0===r)o=i?[1317,8,29]:[1900,1,0],s=6;else{r>60&&--r;var l=new Date(1900,0,1);l.setDate(l.getDate()+r-1),o=[l.getFullYear(),l.getMonth()+1,l.getDate()],s=l.getDay(),r<60&&(s=(s+6)%7),i&&(s=function(t,e){e[0]-=581;var i=t.getDay();return t<60&&(i=(i+6)%7),i}(l,o))}return a.y=o[0],a.m=o[1],a.d=o[2],a.S=n%60,n=Math.floor(n/60),a.M=n%60,n=Math.floor(n/60),a.H=n,a.q=s,a}y[5]=y[63]='"$"#,##0_);\\("$"#,##0\\)',y[6]=y[64]='"$"#,##0_);[Red]\\("$"#,##0\\)',y[7]=y[65]='"$"#,##0.00_);\\("$"#,##0.00\\)',y[8]=y[66]='"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',y[41]='_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',y[42]='_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',y[43]='_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',y[44]='_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)',t.parse_date_code=b;var g=new Date(1899,11,31,0,0,0),_=g.getTime(),v=new Date(1900,2,1,0,0,0);function w(t,e){var i=t.getTime();return e?i-=1262304e5:t>=v&&(i+=864e5),(i-(_+6e4*(t.getTimezoneOffset()-g.getTimezoneOffset())))/864e5}t._general_int=function(t){return t.toString(10)};var I=function(){var t=/(?:\.0*|(\.\d*[1-9])0+)$/;function e(e){return-1==e.indexOf(".")?e:e.replace(t,"$1")}var i=/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,r=/(E[+-])(\d)$/;return function(t){var n,s=Math.floor(Math.log(Math.abs(t))*Math.LOG10E);return n=s>=-4&&s<=-1?t.toPrecision(10+s):Math.abs(s)<=9?function(t){var i=t<0?12:11,r=e(t.toFixed(12));return r.length<=i||(r=t.toPrecision(10)).length<=i?r:t.toExponential(5)}(t):10===s?t.toFixed(10).substr(0,12):function(t){var i=e(t.toFixed(11));return i.length>(t<0?12:11)||"0"===i||"-0"===i?t.toPrecision(6):i}(t),e(function(t){return-1==t.indexOf("E")?t:t.replace(i,"$1E").replace(r,"$10$2")}(n.toUpperCase()))}}();function S(t,e){switch(typeof t){case"string":return t;case"boolean":return t?"TRUE":"FALSE";case"number":return(0|t)===t?t.toString(10):I(t);case"undefined":return"";case"object":if(null==t)return"";if(t instanceof Date)return R(14,w(t,e&&e.date1904),e)}throw new Error("unsupported value in General format: "+t)}function T(t,e,i,n){var s,o="",a=0,l=0,h=i.y,d=0;switch(t){case 98:h=i.y+543;case 121:switch(e.length){case 1:case 2:s=h%100,d=2;break;default:s=h%1e4,d=4}break;case 109:switch(e.length){case 1:case 2:s=i.m,d=e.length;break;case 3:return u[i.m-1][1];case 5:return u[i.m-1][0];default:return u[i.m-1][2]}break;case 100:switch(e.length){case 1:case 2:s=i.d,d=e.length;break;case 3:return c[i.q][0];default:return c[i.q][1]}break;case 104:switch(e.length){case 1:case 2:s=1+(i.H+11)%12,d=e.length;break;default:throw"bad hour format: "+e}break;case 72:switch(e.length){case 1:case 2:s=i.H,d=e.length;break;default:throw"bad hour format: "+e}break;case 77:switch(e.length){case 1:case 2:s=i.M,d=e.length;break;default:throw"bad minute format: "+e}break;case 115:if("s"!=e&&"ss"!=e&&".0"!=e&&".00"!=e&&".000"!=e)throw"bad second format: "+e;return 0!==i.u||"s"!=e&&"ss"!=e?(l=n>=2?3===n?1e3:100:1===n?10:1,(a=Math.round(l*(i.S+i.u)))>=60*l&&(a=0),"s"===e?0===a?"0":""+a/l:(o=r(a,2+n),"ss"===e?o.substr(0,2):"."+o.substr(2,e.length-1))):r(i.S,e.length);case 90:switch(e){case"[h]":case"[hh]":s=24*i.D+i.H;break;case"[m]":case"[mm]":s=60*(24*i.D+i.H)+i.M;break;case"[s]":case"[ss]":s=60*(60*(24*i.D+i.H)+i.M)+Math.round(i.S+i.u);break;default:throw"bad abstime format: "+e}d=3===e.length?1:2;break;case 101:s=h,d=1}return d>0?r(s,d):""}function E(t){if(t.length<=3)return t;for(var e=t.length%3,i=t.substr(0,e);e!=t.length;e+=3)i+=(i.length>0?",":"")+t.substr(e,3);return i}t._general_num=I,t._general=S;var A=function(){var t=/%/g;function o(t,e){var i,r=t.indexOf("E")-t.indexOf(".")-1;if(t.match(/^#+0.0E\+0$/)){if(0==e)return"0.0E+0";if(e<0)return"-"+o(t,-e);var n=t.indexOf(".");-1===n&&(n=t.indexOf("E"));var s=Math.floor(Math.log(e)*Math.LOG10E)%n;if(s<0&&(s+=n),-1===(i=(e/Math.pow(10,s)).toPrecision(r+1+(n+s)%n)).indexOf("e")){var a=Math.floor(Math.log(e)*Math.LOG10E);for(-1===i.indexOf(".")?i=i.charAt(0)+"."+i.substr(1)+"E+"+(a-i.length+s):i+="E+"+(a-s);"0."===i.substr(0,2);)i=(i=i.charAt(0)+i.substr(2,n)+"."+i.substr(2+n)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");i=i.replace(/\+-/,"-")}i=i.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(t,e,i,r){return e+i+r.substr(0,(n+s)%n)+"."+r.substr(s)+"E"}))}else i=e.toExponential(r);return t.match(/E\+00$/)&&i.match(/e[+-]\d$/)&&(i=i.substr(0,i.length-1)+"0"+i.charAt(i.length-1)),t.match(/E\-/)&&i.match(/e\+/)&&(i=i.replace(/e\+/,"e")),i.replace("e","E")}var l=/# (\?+)( ?)\/( ?)(\d+)/,c=/^#*0*\.([0#]+)/,u=/\).*[0#]/,h=/\(###\) ###\\?-####/;function d(t){for(var e,i="",r=0;r!=t.length;++r)switch(e=t.charCodeAt(r)){case 35:break;case 63:i+=" ";break;case 48:i+="0";break;default:i+=String.fromCharCode(e)}return i}function f(t,e){var i=Math.pow(10,e);return""+Math.round(t*i)/i}function p(t,e){var i=t-Math.floor(t),r=Math.pow(10,e);return e<(""+Math.round(i*r)).length?0:Math.round(i*r)}function y(b,g,_){if(40===b.charCodeAt(0)&&!g.match(u)){var v=g.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return _>=0?y("n",v,_):"("+y("n",v,-_)+")"}if(44===g.charCodeAt(g.length-1))return function(t,e,i){for(var r=e.length-1;44===e.charCodeAt(r-1);)--r;return A(t,e.substr(0,r),i/Math.pow(10,3*(e.length-r)))}(b,g,_);if(-1!==g.indexOf("%"))return function(e,r,n){var s=r.replace(t,""),o=r.length-s.length;return A(e,s,n*Math.pow(10,2*o))+i("%",o)}(b,g,_);if(-1!==g.indexOf("E"))return o(g,_);if(36===g.charCodeAt(0))return"$"+y(b,g.substr(" "==g.charAt(1)?2:1),_);var w,I,S,T,D=Math.abs(_),O=_<0?"-":"";if(g.match(/^00+$/))return O+a(D,g.length);if(g.match(/^[#?]+$/))return"0"===(w=a(_,0))&&(w=""),w.length>g.length?w:d(g.substr(0,g.length-w.length))+w;if(I=g.match(l))return function(t,e,s){var o=parseInt(t[4],10),a=Math.round(e*o),l=Math.floor(a/o),c=a-l*o,u=o;return s+(0===l?"":""+l)+" "+(0===c?i(" ",t[1].length+1+t[4].length):n(c,t[1].length)+t[2]+"/"+t[3]+r(u,t[4].length))}(I,D,O);if(g.match(/^#+0+$/))return O+a(D,g.length-g.indexOf("0"));if(I=g.match(c))return w=f(_,I[1].length).replace(/^([^\.]+)$/,"$1."+d(I[1])).replace(/\.$/,"."+d(I[1])).replace(/\.(\d*)$/,(function(t,e){return"."+e+i("0",d(I[1]).length-e.length)})),-1!==g.indexOf("0.")?w:w.replace(/^0\./,".");if(g=g.replace(/^#+([0.])/,"$1"),I=g.match(/^(0*)\.(#*)$/))return O+f(D,I[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,I[1].length?"0.":".");if(I=g.match(/^#{1,3},##0(\.?)$/))return O+E(a(D,0));if(I=g.match(/^#,##0\.([#0]*0)$/))return _<0?"-"+y(b,g,-_):E(""+(Math.floor(_)+function(t,e){return e<(""+Math.round((t-Math.floor(t))*Math.pow(10,e))).length?1:0}(_,I[1].length)))+"."+r(p(_,I[1].length),I[1].length);if(I=g.match(/^#,#*,#0/))return y(b,g.replace(/^#,#*,/,""),_);if(I=g.match(/^([0#]+)(\\?-([0#]+))+$/))return w=e(y(b,g.replace(/[\\-]/g,""),_)),S=0,e(e(g.replace(/\\/g,"")).replace(/[0#]/g,(function(t){return S<w.length?w.charAt(S++):"0"===t?"0":""})));if(g.match(h))return"("+(w=y(b,"##########",_)).substr(0,3)+") "+w.substr(3,3)+"-"+w.substr(6);var M="";if(I=g.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return S=Math.min(I[4].length,7),T=m(D,Math.pow(10,S)-1,!1),w=""+O," "==(M=A("n",I[1],T[1])).charAt(M.length-1)&&(M=M.substr(0,M.length-1)+"0"),w+=M+I[2]+"/"+I[3],(M=s(T[2],S)).length<I[4].length&&(M=d(I[4].substr(I[4].length-M.length))+M),w+=M;if(I=g.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return S=Math.min(Math.max(I[1].length,I[4].length),7),O+((T=m(D,Math.pow(10,S)-1,!0))[0]||(T[1]?"":"0"))+" "+(T[1]?n(T[1],S)+I[2]+"/"+I[3]+s(T[2],S):i(" ",2*S+1+I[2].length+I[3].length));if(I=g.match(/^[#0?]+$/))return w=a(_,0),g.length<=w.length?w:d(g.substr(0,g.length-w.length))+w;if(I=g.match(/^([#0?]+)\.([#0]+)$/)){w=""+_.toFixed(Math.min(I[2].length,10)).replace(/([^0])0+$/,"$1"),S=w.indexOf(".");var N=g.indexOf(".")-S,F=g.length-w.length-N;return d(g.substr(0,N)+w+g.substr(g.length-F))}if(I=g.match(/^00,000\.([#0]*0)$/))return S=p(_,I[1].length),_<0?"-"+y(b,g,-_):E(function(t){return t<2147483647&&t>-2147483648?""+(t>=0?0|t:t-1|0):""+Math.floor(t)}(_)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(t){return"00,"+(t.length<3?r(0,3-t.length):"")+t}))+"."+r(S,I[1].length);switch(g){case"###,##0.00":return y(b,"#,##0.00",_);case"###,###":case"##,###":case"#,###":var C=E(a(D,0));return"0"!==C?O+C:"";case"###,###.00":return y(b,"###,##0.00",_).replace(/^0\./,".");case"#,###.00":return y(b,"#,##0.00",_).replace(/^0\./,".")}throw new Error("unsupported format |"+g+"|")}function b(t,e){var i,r=t.indexOf("E")-t.indexOf(".")-1;if(t.match(/^#+0.0E\+0$/)){if(0==e)return"0.0E+0";if(e<0)return"-"+b(t,-e);var n=t.indexOf(".");-1===n&&(n=t.indexOf("E"));var s=Math.floor(Math.log(e)*Math.LOG10E)%n;if(s<0&&(s+=n),!(i=(e/Math.pow(10,s)).toPrecision(r+1+(n+s)%n)).match(/[Ee]/)){var o=Math.floor(Math.log(e)*Math.LOG10E);-1===i.indexOf(".")?i=i.charAt(0)+"."+i.substr(1)+"E+"+(o-i.length+s):i+="E+"+(o-s),i=i.replace(/\+-/,"-")}i=i.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(t,e,i,r){return e+i+r.substr(0,(n+s)%n)+"."+r.substr(s)+"E"}))}else i=e.toExponential(r);return t.match(/E\+00$/)&&i.match(/e[+-]\d$/)&&(i=i.substr(0,i.length-1)+"0"+i.charAt(i.length-1)),t.match(/E\-/)&&i.match(/e\+/)&&(i=i.replace(/e\+/,"e")),i.replace("e","E")}function g(o,a,f){if(40===o.charCodeAt(0)&&!a.match(u)){var p=a.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return f>=0?g("n",p,f):"("+g("n",p,-f)+")"}if(44===a.charCodeAt(a.length-1))return function(t,e,i){for(var r=e.length-1;44===e.charCodeAt(r-1);)--r;return A(t,e.substr(0,r),i/Math.pow(10,3*(e.length-r)))}(o,a,f);if(-1!==a.indexOf("%"))return function(e,r,n){var s=r.replace(t,""),o=r.length-s.length;return A(e,s,n*Math.pow(10,2*o))+i("%",o)}(o,a,f);if(-1!==a.indexOf("E"))return b(a,f);if(36===a.charCodeAt(0))return"$"+g(o,a.substr(" "==a.charAt(1)?2:1),f);var y,_,v,w,I=Math.abs(f),S=f<0?"-":"";if(a.match(/^00+$/))return S+r(I,a.length);if(a.match(/^[#?]+$/))return y=""+f,0===f&&(y=""),y.length>a.length?y:d(a.substr(0,a.length-y.length))+y;if(_=a.match(l))return function(t,e,r){return r+(0===e?"":""+e)+i(" ",t[1].length+2+t[4].length)}(_,I,S);if(a.match(/^#+0+$/))return S+r(I,a.length-a.indexOf("0"));if(_=a.match(c))return y=(y=(""+f).replace(/^([^\.]+)$/,"$1."+d(_[1])).replace(/\.$/,"."+d(_[1]))).replace(/\.(\d*)$/,(function(t,e){return"."+e+i("0",d(_[1]).length-e.length)})),-1!==a.indexOf("0.")?y:y.replace(/^0\./,".");if(a=a.replace(/^#+([0.])/,"$1"),_=a.match(/^(0*)\.(#*)$/))return S+(""+I).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,_[1].length?"0.":".");if(_=a.match(/^#{1,3},##0(\.?)$/))return S+E(""+I);if(_=a.match(/^#,##0\.([#0]*0)$/))return f<0?"-"+g(o,a,-f):E(""+f)+"."+i("0",_[1].length);if(_=a.match(/^#,#*,#0/))return g(o,a.replace(/^#,#*,/,""),f);if(_=a.match(/^([0#]+)(\\?-([0#]+))+$/))return y=e(g(o,a.replace(/[\\-]/g,""),f)),v=0,e(e(a.replace(/\\/g,"")).replace(/[0#]/g,(function(t){return v<y.length?y.charAt(v++):"0"===t?"0":""})));if(a.match(h))return"("+(y=g(o,"##########",f)).substr(0,3)+") "+y.substr(3,3)+"-"+y.substr(6);var T="";if(_=a.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return v=Math.min(_[4].length,7),w=m(I,Math.pow(10,v)-1,!1),y=""+S," "==(T=A("n",_[1],w[1])).charAt(T.length-1)&&(T=T.substr(0,T.length-1)+"0"),y+=T+_[2]+"/"+_[3],(T=s(w[2],v)).length<_[4].length&&(T=d(_[4].substr(_[4].length-T.length))+T),y+=T;if(_=a.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return v=Math.min(Math.max(_[1].length,_[4].length),7),S+((w=m(I,Math.pow(10,v)-1,!0))[0]||(w[1]?"":"0"))+" "+(w[1]?n(w[1],v)+_[2]+"/"+_[3]+s(w[2],v):i(" ",2*v+1+_[2].length+_[3].length));if(_=a.match(/^[#0?]+$/))return y=""+f,a.length<=y.length?y:d(a.substr(0,a.length-y.length))+y;if(_=a.match(/^([#0]+)\.([#0]+)$/)){y=""+f.toFixed(Math.min(_[2].length,10)).replace(/([^0])0+$/,"$1"),v=y.indexOf(".");var D=a.indexOf(".")-v,O=a.length-y.length-D;return d(a.substr(0,D)+y+a.substr(a.length-O))}if(_=a.match(/^00,000\.([#0]*0)$/))return f<0?"-"+g(o,a,-f):E(""+f).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(t){return"00,"+(t.length<3?r(0,3-t.length):"")+t}))+"."+r(0,_[1].length);switch(a){case"###,###":case"##,###":case"#,###":var M=E(""+I);return"0"!==M?S+M:"";default:if(a.match(/\.[0#?]*$/))return g(o,a.slice(0,a.lastIndexOf(".")),f)+d(a.slice(a.lastIndexOf(".")))}throw new Error("unsupported format |"+a+"|")}return function(t,e,i){return(0|i)===i?g(t,e,i):y(t,e,i)}}();function D(t){for(var e=[],i=!1,r=0,n=0;r<t.length;++r)switch(t.charCodeAt(r)){case 34:i=!i;break;case 95:case 42:case 92:++r;break;case 59:e[e.length]=t.substr(n,r-n),n=r+1}if(e[e.length]=t.substr(n),!0===i)throw new Error("Format |"+t+"| unterminated string ");return e}t._split=D;var O=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function M(t){for(var e=0,i="",r="";e<t.length;)switch(i=t.charAt(e)){case"G":l(t,e)&&(e+=6),e++;break;case'"':for(;34!==t.charCodeAt(++e)&&e<t.length;);++e;break;case"\\":case"_":e+=2;break;case"@":++e;break;case"B":case"b":if("1"===t.charAt(e+1)||"2"===t.charAt(e+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===t.substr(e,3).toUpperCase()||"AM/PM"===t.substr(e,5).toUpperCase()||"上午/下午"===t.substr(e,5).toUpperCase())return!0;++e;break;case"[":for(r=i;"]"!==t.charAt(e++)&&e<t.length;)r+=t.charAt(e);if(r.match(O))return!0;break;case".":case"0":case"#":for(;e<t.length&&("0#?.,E+-%".indexOf(i=t.charAt(++e))>-1||"\\"==i&&"-"==t.charAt(e+1)&&"0#".indexOf(t.charAt(e+2))>-1););break;case"?":for(;t.charAt(++e)===i;);break;case"*":++e,(" "==t.charAt(e)||"*"==t.charAt(e))&&++e;break;case"(":case")":++e;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;e<t.length&&"0123456789".indexOf(t.charAt(++e))>-1;);break;default:++e}return!1}function N(t,e,i,r){for(var n,s,o,a=[],c="",u=0,h="",d="t",f="H";u<t.length;)switch(h=t.charAt(u)){case"G":if(!l(t,u))throw new Error("unrecognized character "+h+" in "+t);a[a.length]={t:"G",v:"General"},u+=7;break;case'"':for(c="";34!==(o=t.charCodeAt(++u))&&u<t.length;)c+=String.fromCharCode(o);a[a.length]={t:"t",v:c},++u;break;case"\\":var p=t.charAt(++u),y="("===p||")"===p?p:"t";a[a.length]={t:y,v:p},++u;break;case"_":a[a.length]={t:"t",v:" "},u+=2;break;case"@":a[a.length]={t:"T",v:e},++u;break;case"B":case"b":if("1"===t.charAt(u+1)||"2"===t.charAt(u+1)){if(null==n&&null==(n=b(e,i,"2"===t.charAt(u+1))))return"";a[a.length]={t:"X",v:t.substr(u,2)},d=h,u+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":h=h.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(e<0||null==n&&null==(n=b(e,i)))return"";for(c=h;++u<t.length&&t.charAt(u).toLowerCase()===h;)c+=h;"m"===h&&"h"===d.toLowerCase()&&(h="M"),"h"===h&&(h=f),a[a.length]={t:h,v:c},d=h;break;case"A":case"a":case"上":var m={t:h,v:h};if(null==n&&(n=b(e,i)),"A/P"===t.substr(u,3).toUpperCase()?(null!=n&&(m.v=n.H>=12?"P":"A"),m.t="T",f="h",u+=3):"AM/PM"===t.substr(u,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"PM":"AM"),m.t="T",u+=5,f="h"):"上午/下午"===t.substr(u,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"下午":"上午"),m.t="T",u+=5,f="h"):(m.t="t",++u),null==n&&"T"===m.t)return"";a[a.length]=m,d=h;break;case"[":for(c=h;"]"!==t.charAt(u++)&&u<t.length;)c+=t.charAt(u);if("]"!==c.slice(-1))throw'unterminated "[" block: |'+c+"|";if(c.match(O)){if(null==n&&null==(n=b(e,i)))return"";a[a.length]={t:"Z",v:c.toLowerCase()},d=c.charAt(1)}else c.indexOf("$")>-1&&(c=(c.match(/\$([^-\[\]]*)/)||[])[1]||"$",M(t)||(a[a.length]={t:"t",v:c}));break;case".":if(null!=n){for(c=h;++u<t.length&&"0"===(h=t.charAt(u));)c+=h;a[a.length]={t:"s",v:c};break}case"0":case"#":for(c=h;++u<t.length&&"0#?.,E+-%".indexOf(h=t.charAt(u))>-1;)c+=h;a[a.length]={t:"n",v:c};break;case"?":for(c=h;t.charAt(++u)===h;)c+=h;a[a.length]={t:h,v:c},d=h;break;case"*":++u,(" "==t.charAt(u)||"*"==t.charAt(u))&&++u;break;case"(":case")":a[a.length]={t:1===r?"t":h,v:h},++u;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(c=h;u<t.length&&"0123456789".indexOf(t.charAt(++u))>-1;)c+=t.charAt(u);a[a.length]={t:"D",v:c};break;case" ":a[a.length]={t:h,v:h},++u;break;case"$":a[a.length]={t:"t",v:"$"},++u;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(h))throw new Error("unrecognized character "+h+" in "+t);a[a.length]={t:"t",v:h},++u}var g,_=0,v=0;for(u=a.length-1,d="t";u>=0;--u)switch(a[u].t){case"h":case"H":a[u].t=f,d="h",_<1&&(_=1);break;case"s":(g=a[u].v.match(/\.0+$/))&&(v=Math.max(v,g[0].length-1)),_<3&&(_=3);case"d":case"y":case"M":case"e":d=a[u].t;break;case"m":"s"===d&&(a[u].t="M",_<2&&(_=2));break;case"X":break;case"Z":_<1&&a[u].v.match(/[Hh]/)&&(_=1),_<2&&a[u].v.match(/[Mm]/)&&(_=2),_<3&&a[u].v.match(/[Ss]/)&&(_=3)}switch(_){case 0:break;case 1:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M),n.M>=60&&(n.M=0,++n.H);break;case 2:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M)}var w,I="";for(u=0;u<a.length;++u)switch(a[u].t){case"t":case"T":case" ":case"D":break;case"X":a[u].v="",a[u].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":a[u].v=T(a[u].t.charCodeAt(0),a[u].v,n,v),a[u].t="t";break;case"n":case"?":for(w=u+1;null!=a[w]&&("?"===(h=a[w].t)||"D"===h||(" "===h||"t"===h)&&null!=a[w+1]&&("?"===a[w+1].t||"t"===a[w+1].t&&"/"===a[w+1].v)||"("===a[u].t&&(" "===h||"n"===h||")"===h)||"t"===h&&("/"===a[w].v||" "===a[w].v&&null!=a[w+1]&&"?"==a[w+1].t));)a[u].v+=a[w].v,a[w]={v:"",t:";"},++w;I+=a[u].v,u=w-1;break;case"G":a[u].t="t",a[u].v=S(e,i)}var E,D,N="";if(I.length>0){40==I.charCodeAt(0)?(E=e<0&&45===I.charCodeAt(0)?-e:e,D=A("n",I,E)):(D=A("n",I,E=e<0&&r>1?-e:e),E<0&&a[0]&&"t"==a[0].t&&(D=D.substr(1),a[0].v="-"+a[0].v)),w=D.length-1;var F=a.length;for(u=0;u<a.length;++u)if(null!=a[u]&&"t"!=a[u].t&&a[u].v.indexOf(".")>-1){F=u;break}var C=a.length;if(F===a.length&&-1===D.indexOf("E")){for(u=a.length-1;u>=0;--u)null==a[u]||-1==="n?".indexOf(a[u].t)||(w>=a[u].v.length-1?(w-=a[u].v.length,a[u].v=D.substr(w+1,a[u].v.length)):w<0?a[u].v="":(a[u].v=D.substr(0,w+1),w=-1),a[u].t="t",C=u);w>=0&&C<a.length&&(a[C].v=D.substr(0,w+1)+a[C].v)}else if(F!==a.length&&-1===D.indexOf("E")){for(w=D.indexOf(".")-1,u=F;u>=0;--u)if(null!=a[u]&&-1!=="n?".indexOf(a[u].t)){for(s=a[u].v.indexOf(".")>-1&&u===F?a[u].v.indexOf(".")-1:a[u].v.length-1,N=a[u].v.substr(s+1);s>=0;--s)w>=0&&("0"===a[u].v.charAt(s)||"#"===a[u].v.charAt(s))&&(N=D.charAt(w--)+N);a[u].v=N,a[u].t="t",C=u}for(w>=0&&C<a.length&&(a[C].v=D.substr(0,w+1)+a[C].v),w=D.indexOf(".")+1,u=F;u<a.length;++u)if(null!=a[u]&&(-1!=="n?(".indexOf(a[u].t)||u===F)){for(s=a[u].v.indexOf(".")>-1&&u===F?a[u].v.indexOf(".")+1:0,N=a[u].v.substr(0,s);s<a[u].v.length;++s)w<D.length&&(N+=D.charAt(w++));a[u].v=N,a[u].t="t",C=u}}}for(u=0;u<a.length;++u)null!=a[u]&&"n?".indexOf(a[u].t)>-1&&(E=r>1&&e<0&&u>0&&"-"===a[u-1].v?-e:e,a[u].v=A(a[u].t,a[u].v,E),a[u].t="t");var B="";for(u=0;u!==a.length;++u)null!=a[u]&&(B+=a[u].v);return B}t.is_date=M,t._eval=N;var F=/\[[=<>]/,C=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function B(t,e){if(null==e)return!1;var i=parseFloat(e[2]);switch(e[1]){case"=":if(t==i)return!0;break;case">":if(t>i)return!0;break;case"<":if(t<i)return!0;break;case"<>":if(t!=i)return!0;break;case">=":if(t>=i)return!0;break;case"<=":if(t<=i)return!0}return!1}function R(t,e,i){null==i&&(i={});var r="";switch(typeof t){case"string":r="m/d/yy"==t&&i.dateNF?i.dateNF:t;break;case"number":null==(r=14==t&&i.dateNF?i.dateNF:(null!=i.table?i.table:d)[t])&&(r=i.table&&i.table[f[t]]||d[f[t]]),null==r&&(r=y[t]||"General")}if(l(r,0))return S(e,i);e instanceof Date&&(e=w(e,i.date1904));var n=function(t,e){var i=D(t),r=i.length,n=i[r-1].indexOf("@");if(r<4&&n>-1&&--r,i.length>4)throw new Error("cannot find right format for |"+i.join("|")+"|");if("number"!=typeof e)return[4,4===i.length||n>-1?i[i.length-1]:"@"];switch(i.length){case 1:i=n>-1?["General","General","General",i[0]]:[i[0],i[0],i[0],"@"];break;case 2:i=n>-1?[i[0],i[0],i[0],i[1]]:[i[0],i[1],i[0],"@"];break;case 3:i=n>-1?[i[0],i[1],i[0],i[2]]:[i[0],i[1],i[2],"@"]}var s=e>0?i[0]:e<0?i[1]:i[2];if(-1===i[0].indexOf("[")&&-1===i[1].indexOf("["))return[r,s];if(null!=i[0].match(F)||null!=i[1].match(F)){var o=i[0].match(C),a=i[1].match(C);return B(e,o)?[r,i[0]]:B(e,a)?[r,i[1]]:[r,i[null!=o&&null!=a?2:1]]}return[r,s]}(r,e);if(l(n[1]))return S(e,i);if(!0===e)e="TRUE";else if(!1===e)e="FALSE";else if(""===e||null==e)return"";return N(n[1],e,i,n[0])}function L(t,e){if("number"!=typeof e){e=+e||-1;for(var i=0;i<392;++i)if(null!=d[i]){if(d[i]==t){e=i;break}}else e<0&&(e=i);e<0&&(e=391)}return d[e]=t,e}t.load=L,t._table=d,t.get_table=function(){return d},t.load_table=function(t){for(var e=0;392!=e;++e)void 0!==t[e]&&L(t[e],e)},t.init_table=h,t.format=R})(e),typeof DO_NOT_EXPORT_SSF>"u"&&(t.exports=e)}(Dl);var Rf=Dl.exports;const Ni=Nf(Rf),op="customFormattingSettings",nn="auto",Mf=3,Cf=[{name:"year",description:'When lowerCase(columnName)="year" with the column having numeric values will result in no formatting',matchingFunction:(t,e,i)=>!(!t||!e||"year"!==t.toLowerCase()||"number"!==(null==e?void 0:e.evidenceType)&&"number"!==(null==i?void 0:i.unitType)),format:{formatCode:nn,valueType:"number",exampleInput:2013,_autoFormat:{autoFormatCode:"@",truncateUnits:!1}}},{name:"id",description:'When lowerCase(columnName)="id" with the column having numeric values, then values will have no formatting',matchingFunction:(t,e,i)=>!(!t||!e||"id"!==t.toLowerCase()||"number"!==(null==e?void 0:e.evidenceType)&&"number"!==(null==i?void 0:i.unitType)),format:{formatCode:nn,valueType:"number",exampleInput:93120121,_autoFormat:{autoFormatFunction:t=>null==t||isNaN(t)?t:t.toLocaleString("fullwide",{useGrouping:!1})}}},{name:"defaultDate",description:"Formatting for Default Date",matchingFunction:(t,e,i)=>!!e&&("date"===(null==e?void 0:e.evidenceType)||"date"===(null==i?void 0:i.unitType)),format:{formatCode:nn,valueType:"date",exampleInput:"Sat Jan 01 2022 03:15:00 GMT-0500",_autoFormat:{autoFormatCode:"YYYY-MM-DD",truncateUnits:!1}}}],mo=(t,e)=>{switch(e){case"T":return t/1e12;case"B":return t/1e9;case"M":return t/1e6;case"k":return t/1e3;default:return t}},ap=(t,e)=>{var i,r,n;let s=(null==(i=e||t.formatCode)?void 0:i.toLowerCase())===nn,o=(null==(r=t._autoFormat)?void 0:r.autoFormatFunction)||(null==(n=t._autoFormat)?void 0:n.autoFormatCode);return!(!s||void 0===o)},go=(t,e=7)=>{let i,r,n="",s=null==t?void 0:t.median;if(void 0!==s){let o;n=Nl(s),n?(o=mo(s,n),r=!0):(o=s,r=!1),i=0!==t.maxDecimals||r?Lf(o,e):"#,##0"}else i="#,##0",r=!1;return{formatCode:nn,valueType:"number",_autoFormat:{autoFormatCode:i,truncateUnits:r,columnUnits:n}}},cp=(t,e,i)=>{let r=Cf.find((r=>r.matchingFunction(t,e,i)));return r?r.format:"number"===(null==i?void 0:i.unitType)?go(i):void 0},lp=(t,e,i=void 0)=>{var r,n,s;if(null!=(r=e._autoFormat)&&r.autoFormatFunction)return e._autoFormat.autoFormatFunction(t,e,i);if(e._autoFormat.autoFormatCode){let r=null==(n=null==e?void 0:e._autoFormat)?void 0:n.autoFormatCode;if("number"===e.valueType){let n=t,o="";return(null==(s=null==e?void 0:e._autoFormat)?void 0:s.truncateUnits)&&void 0!==(null==i?void 0:i.median)&&(o=Nl(i.median),n=mo(t,o)),Ni.format(r,n)+o}return Ni.format(r,t)}return console.warn("autoFormat called without a formatCode or function"),t},up=t=>"number"==typeof t?t.toLocaleString(void 0,{minimumFractionDigits:0,maximumFractionDigits:2}):null!=t?null==t?void 0:t.toString():"-";function Lf(t,e=7,i=3){let r="#,##0",n=Uf(t),s=0;return n-i<0&&(s=Math.min(Math.max(Math.abs(n-i+1),0),e)),s>0&&(r+=".",r+="0".repeat(s)),r}function Nl(t){let e=Math.abs(t);return e>=5e12?"T":e>=5e9?"B":e>=5e6?"M":e>=5e3?"k":""}function Uf(t){return 0===t?0:Math.floor(Math.log10(t))}const xf=[{primaryCode:"usd",currencySymbol:"$",displayName:"USD - United States Dollar"},{primaryCode:"aud",currencySymbol:"A$",displayName:"AUD - Australian Dollar",escapeCurrencySymbol:!0},{primaryCode:"brl",currencySymbol:"R$",displayName:"BRL - Brazilian Real",escapeCurrencySymbol:!0},{primaryCode:"cad",currencySymbol:"C$",displayName:"CAD - Canadian Dollar",escapeCurrencySymbol:!0},{primaryCode:"cny",currencySymbol:"¥",displayName:"CNY - Renminbi",escapeCurrencySymbol:!0},{primaryCode:"eur",currencySymbol:"€",displayName:"EUR - Euro"},{primaryCode:"gbp",currencySymbol:"£",displayName:"GBP - Pound Sterling",escapeCurrencySymbol:!0},{primaryCode:"jpy",currencySymbol:"¥",displayName:"JPY - Japanese Yen",escapeCurrencySymbol:!0},{primaryCode:"inr",currencySymbol:"₹",displayName:"INR - Indian Rupee",escapeCurrencySymbol:!0},{primaryCode:"krw",currencySymbol:"₩",displayName:"KRW - South Korean won",escapeCurrencySymbol:!0},{primaryCode:"ngn",currencySymbol:"₦",displayName:"NGN -  Nigerian Naira",escapeCurrencySymbol:!0},{primaryCode:"rub",currencySymbol:"rub",displayName:"RUB - Russian Ruble",escapeCurrencySymbol:!0},{primaryCode:"sek",currencySymbol:"kr",displayName:"SEK - Swedish Krona",escapeCurrencySymbol:!0}],Pf=[{derivedSuffix:"",valueFormatCode:"#,##0",exampleInput:412.17,auto:!0},{derivedSuffix:"0",valueFormatCode:"#,##0",exampleInput:7043.123},{derivedSuffix:"1",valueFormatCode:"#,##0.0",exampleInput:7043.123},{derivedSuffix:"2",valueFormatCode:"#,##0.00",exampleInput:7043.123},{derivedSuffix:"0k",valueFormatCode:'#,##0,"k"',exampleInput:64301.12},{derivedSuffix:"1k",valueFormatCode:'#,##0.0,"k"',exampleInput:64301.12},{derivedSuffix:"2k",valueFormatCode:'#,##0.00,"k"',exampleInput:64301.12},{derivedSuffix:"0m",valueFormatCode:'#,##0,,"M"',exampleInput:4564301.12},{derivedSuffix:"1m",valueFormatCode:'#,##0.0,,"M"',exampleInput:4564301.12},{derivedSuffix:"2m",valueFormatCode:'#,##0.00,,"M"',exampleInput:4564301.12},{derivedSuffix:"0b",valueFormatCode:'#,##0,,,"B"',exampleInput:9784564301.12},{derivedSuffix:"1b",valueFormatCode:'#,##0.0,,,"B"',exampleInput:9784564301.12},{derivedSuffix:"2b",valueFormatCode:'#,##0.00,,,"B"',exampleInput:9784564301.12}],kf=xf.map((t=>{let e=[];return Pf.forEach((i=>{let r={formatTag:t.primaryCode+i.derivedSuffix,parentFormat:t.primaryCode,formatCategory:"currency",valueType:"number",exampleInput:i.exampleInput,titleTagReplacement:` (${t.currencySymbol})`},n=t.escapeCurrencySymbol?`"${t.currencySymbol}"`:t.currencySymbol;i.auto||nn===i.formatCode?(r.formatCode=nn,r._autoFormat={autoFormatFunction:(t,e,i)=>{let r=go(i,2),s=`${n}${r._autoFormat.autoFormatCode}`,o="",a=t;return r._autoFormat.truncateUnits&&r._autoFormat.columnUnits?(o=r._autoFormat.columnUnits,a=mo(t,r._autoFormat.columnUnits)):s.endsWith(".0")&&(s+="0"),Ni.format(s,a)+o}}):r.formatCode=`${n}${i.valueFormatCode}`,i.axisValueFormatCode&&(r.axisFormatCode=i.axisValueFormatCode),e.push(r)})),e})).flat(),hp=[...kf,{formatTag:"ddd",formatCode:"ddd",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"dddd",formatCode:"dddd",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"mmm",formatCode:"mmm",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"mmmm",formatCode:"mmmm",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"yyyy",formatCode:"yyyy",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"shortdate",formatCode:"mmm d/yy",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"longdate",formatCode:"mmmm d, yyyy",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"fulldate",formatCode:"dddd mmmm d, yyyy",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"mdy",formatCode:"m/d/y",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"dmy",formatCode:"d/m/y",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"hms",formatCode:"H:MM:SS AM/PM",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09T11:45:03"},{formatTag:"num0",formatCode:"#,##0",formatCategory:"number",valueType:"number",exampleInput:11.23168},{formatTag:"num1",formatCode:"#,##0.0",formatCategory:"number",valueType:"number",exampleInput:11.23168},{formatTag:"num2",formatCode:"#,##0.00",formatCategory:"number",valueType:"number",exampleInput:11.23168},{formatTag:"num3",formatCode:"#,##0.000",formatCategory:"number",valueType:"number",exampleInput:11.23168},{formatTag:"num4",formatCode:"#,##0.0000",formatCategory:"number",valueType:"number",exampleInput:11.23168},{formatTag:"num0k",formatCode:'#,##0,"k"',formatCategory:"number",valueType:"number",exampleInput:64201},{formatTag:"num1k",formatCode:'#,##0.0,"k"',formatCategory:"number",valueType:"number",exampleInput:64201},{formatTag:"num2k",formatCode:'#,##0.00,"k"',formatCategory:"number",valueType:"number",exampleInput:64201},{formatTag:"num0m",formatCode:'#,##0,,"M"',formatCategory:"number",valueType:"number",exampleInput:42539483},{formatTag:"num1m",formatCode:'#,##0.0,,"M"',formatCategory:"number",valueType:"number",exampleInput:42539483},{formatTag:"num2m",formatCode:'#,##0.00,,"M"',formatCategory:"number",valueType:"number",exampleInput:42539483},{formatTag:"num0b",formatCode:'#,##0,,,"B"',formatCategory:"number",valueType:"number",exampleInput:1384937584},{formatTag:"num1b",formatCode:'#,##0.0,,,"B"',formatCategory:"number",valueType:"number",exampleInput:1384937584},{formatTag:"num2b",formatCode:'#,##0.00,,,"B"',formatCategory:"number",valueType:"number",exampleInput:1384937584},{formatTag:"id",formatCode:"0",formatCategory:"number",valueType:"number",exampleInput:"921594675",titleTagReplacement:" id"},{formatTag:"fract",formatCode:"# ?/?",formatCategory:"number",valueType:"number",exampleInput:"0.25"},{formatTag:"mult",formatCode:'#,##0.0"x"',formatCategory:"number",valueType:"number",exampleInput:"5.32"},{formatTag:"mult0",formatCode:'#,##0"x"',formatCategory:"number",valueType:"number",exampleInput:"5.32"},{formatTag:"mult1",formatCode:'#,##0.0"x"',formatCategory:"number",valueType:"number",exampleInput:"5.32"},{formatTag:"mult2",formatCode:'#,##0.00"x"',formatCategory:"number",valueType:"number",exampleInput:"5.32"},{formatTag:"sci",formatCode:"0.00E+0",formatCategory:"number",valueType:"number",exampleInput:"16546.1561"},{formatTag:"pct",formatCode:nn,formatCategory:"percent",valueType:"number",exampleInput:.731,titleTagReplacement:"",_autoFormat:{autoFormatFunction:(t,e,i)=>{if("number"===(null==i?void 0:i.unitType)){let e={min:100*i.min,max:100*i.max,median:100*i.median,maxDecimals:Math.max(i.maxDecimals-2,0),unitType:i.unitType},r=go(e);return Ni.format(r._autoFormat.autoFormatCode,100*t)+"%"}return Ni.format("#,##0%",t)}}},{formatTag:"pct0",formatCode:"#,##0%",formatCategory:"percent",valueType:"number",exampleInput:.731,titleTagReplacement:""},{formatTag:"pct1",formatCode:"#,##0.0%",formatCategory:"percent",valueType:"number",exampleInput:.731,titleTagReplacement:""},{formatTag:"pct2",formatCode:"#,##0.00%",formatCategory:"percent",valueType:"number",exampleInput:.731,titleTagReplacement:""},{formatTag:"pct3",formatCode:"#,##0.000%",formatCategory:"percent",valueType:"number",exampleInput:.731,titleTagReplacement:""}];var mn,Vr;!function(t){t.BOOLEAN="boolean",t.NUMBER="number",t.STRING="string",t.DATE="date"}(mn||(mn={})),function(t){t.INFERRED="inferred",t.PRECISE="precise"}(Vr||(Vr={}));const $f=function(t){return"number"==typeof t?mn.NUMBER:"boolean"==typeof t?mn.BOOLEAN:t instanceof Date?mn.DATE:mn.STRING};function dp(t){if(null!=t&&t._evidenceColumnTypes)return t._evidenceColumnTypes;if(t&&t.length>0){let e=Object.keys(t[0]);return null==e?void 0:e.map((e=>{let i=t.find((t=>null!=t[e]));if(i){let t=$f(i[e]);return{name:e,evidenceType:t,typeFidelity:Vr.INFERRED}}return{name:e,evidenceType:mn.STRING,typeFidelity:Vr.INFERRED}}))}return[]}export{Xf as A,hp as B,op as C,cp as D,ap as E,lp as F,up as G,dp as H,Vr as I,mn as J,Us as Q,xf as S,ye as T,Ae as _,np as a,sp as b,Ni as c,q as d,tp as e,Xi as f,In as g,Y as h,Co as i,ip as j,Nf as k,Hf as l,zl as m,po as n,Zf as o,Qf as p,ef as q,Jf as r,rp as s,Kf as t,qf as u,Df as v,ep as w,bi as x,rf as y,Fl as z};