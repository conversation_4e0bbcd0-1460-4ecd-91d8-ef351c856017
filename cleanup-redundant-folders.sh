#!/bin/bash

# Cleanup script for Evidence DDX project
# Removes redundant folders and files to simplify project structure

set -e

echo "🧹 Starting project cleanup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to safely remove directory
safe_remove_dir() {
    local dir="$1"
    if [ -d "$dir" ]; then
        print_status "Removing directory: $dir"
        rm -rf "$dir"
        print_success "Removed: $dir"
    else
        print_warning "Directory not found: $dir"
    fi
}

# Function to safely remove file
safe_remove_file() {
    local file="$1"
    if [ -f "$file" ]; then
        print_status "Removing file: $file"
        rm -f "$file"
        print_success "Removed: $file"
    else
        print_warning "File not found: $file"
    fi
}

# Show current directory structure
show_before_after() {
    echo ""
    echo "=========================================="
    echo "📊 Directory Size Analysis"
    echo "=========================================="
    
    if command -v du &> /dev/null; then
        echo "Current project size:"
        du -sh . 2>/dev/null || echo "Unable to calculate size"
    fi
    
    echo ""
    echo "Directories to be removed:"
    for dir in "evidence-project" "demo" "test-clean" "domo-evidence-clean" "evidence_build"; do
        if [ -d "$dir" ]; then
            size=$(du -sh "$dir" 2>/dev/null | cut -f1 || echo "unknown")
            echo "  - $dir ($size)"
        fi
    done
}

# Main cleanup function
cleanup_project() {
    print_status "Starting cleanup process..."
    
    # Remove redundant Evidence project directories
    print_status "Removing redundant Evidence project directories..."
    safe_remove_dir "evidence-project"
    safe_remove_dir "demo"
    safe_remove_dir "test-clean"
    safe_remove_dir "domo-evidence-clean"
    safe_remove_dir "evidence_build"
    
    # Remove build artifacts
    print_status "Removing build artifacts..."
    safe_remove_file "evidence-ddx-app.zip"
    safe_remove_file "dist/manifest.json.backup"
    
    print_success "Cleanup completed!"
}

# Verify essential files remain
verify_essential_files() {
    print_status "Verifying essential files are intact..."
    
    essential_files=(
        "evidence-project-fresh"
        "package.json"
        "webpack.config.js"
        "build-ddx.sh"
        "manifest.json"
    )
    
    all_good=true
    for item in "${essential_files[@]}"; do
        if [ ! -e "$item" ]; then
            print_error "Essential file/directory missing: $item"
            all_good=false
        fi
    done
    
    if [ "$all_good" = true ]; then
        print_success "All essential files verified!"
    else
        print_error "Some essential files are missing!"
        exit 1
    fi
}

# Show final summary
show_summary() {
    echo ""
    echo "=========================================="
    echo "🎉 Cleanup Summary"
    echo "=========================================="
    echo ""
    print_success "Project cleanup completed successfully!"
    echo ""
    echo "📁 Remaining structure:"
    echo "   - evidence-project-fresh/  (Main Evidence project)"
    echo "   - dist/                    (Build output)"
    echo "   - node_modules/            (Dependencies)"
    echo "   - build-ddx.sh            (Build script)"
    echo "   - webpack.config.js       (Webpack config)"
    echo "   - package.json            (Dependencies)"
    echo "   - Dockerfile              (Docker config)"
    echo "   - manifest.json           (Domo manifest)"
    echo ""
    
    if command -v du &> /dev/null; then
        echo "📊 New project size:"
        du -sh . 2>/dev/null || echo "Unable to calculate size"
    fi
    
    echo ""
    echo "🚀 Next steps:"
    echo "   1. Test the build: ./build-ddx.sh"
    echo "   2. Verify everything works as expected"
    echo "   3. Commit the cleaned-up project"
    echo ""
}

# Main execution
main() {
    echo "=========================================="
    echo "🧹 Evidence DDX Project Cleanup"
    echo "=========================================="
    echo ""
    
    # Show what will be cleaned
    show_before_after
    
    echo ""
    echo "⚠️  This will permanently delete the following directories:"
    echo "   - evidence-project/ (older Evidence version)"
    echo "   - demo/ (demo files)"
    echo "   - test-clean/ (test files)"
    echo "   - domo-evidence-clean/ (superseded clean version)"
    echo "   - evidence_build/ (temporary build copy)"
    echo ""
    echo "✅ These will be preserved:"
    echo "   - evidence-project-fresh/ (main Evidence project)"
    echo "   - dist/ (build output)"
    echo "   - node_modules/ (dependencies)"
    echo "   - All configuration files"
    echo ""
    
    read -p "Do you want to proceed with cleanup? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "Cleanup cancelled by user"
        exit 0
    fi
    
    cleanup_project
    verify_essential_files
    show_summary
}

# Run the main function
main "$@"
