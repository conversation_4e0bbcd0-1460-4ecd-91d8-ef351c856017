# Evidence DuckDB Workflow - Domo DDX App

A clean, working Domo DDX application that integrates Evidence framework concepts with DuckDB for data analysis.

## 🎯 Project Status

✅ **WORKING** - Both `domo dev` and `domo publish` are functional!

## 🔧 Root Cause Resolution

**Problem Identified**: The original "undefined is not a function" error was caused by:
1. **Complex manifest.json** with advanced properties that Domo CLI couldn't parse
2. **File structure mismatch** between Evidence framework build and Domo CLI expectations
3. **Missing required fields** (`fileName` and `id`) in manifest.json

**Solution Applied**:
1. Simplified manifest.json to basic Domo CLI structure
2. Created clean HTML/JS/CSS files compatible with Domo SDK
3. Added proper design ID after publishing

## 📁 Project Structure

```
domo-evidence-clean/
├── manifest.json      # Simplified Domo manifest (WORKING)
├── index.html         # Clean HTML with Domo SDK integration
├── app.js            # JavaScript with Domo API calls
├── styles.css        # Clean, responsive CSS
└── README.md         # This file
```

## 🚀 Quick Start

### Prerequisites
- Domo CLI installed (`npm install -g ryuu`)
- Domo account access
- Node.js v16+ (tested with v24.1.0)

### Development Workflow

1. **Login to Domo**:
   ```bash
   domo login
   ```

2. **Publish the app** (required first time):
   ```bash
   domo publish
   ```

3. **Start development server**:
   ```bash
   domo dev
   ```

4. **Access the app**:
   - Open http://localhost:3000
   - View in Domo at the provided URL

## 📊 Features

- **Domo SDK Integration**: Loads data from configured datasets
- **Real-time Testing**: Shows connection status and sample data
- **Error Handling**: Displays clear error messages
- **Responsive Design**: Works on desktop and mobile
- **Development Mode**: Graceful fallback when Domo SDK unavailable

## 🔗 Dataset Configuration

The app is configured to use two datasets:
- **Excel Dataset**: `a8cf6a39-11f0-4b11-b577-7e8d4bf0efbe`
- **Government Dataset**: `a92b693c-44e6-4d68-b7c9-f98753ca3edc`

To use different datasets, update the `mapping` section in `manifest.json`.

## 🛠 Development Notes

### Key Learnings
1. **Start Simple**: Use basic manifest.json structure first
2. **Publish First**: Design ID is required for `domo dev` to work
3. **Test Incrementally**: Add complexity gradually
4. **Use Domo SDK**: Load `ryuu.js` for Domo API access

### Troubleshooting
- If `domo dev` fails with "DesignId is required", run `domo publish` first
- If "undefined is not a function", check manifest.json syntax
- For data access issues, verify dataset IDs and permissions

## 📈 Next Steps

1. **Add DuckDB Integration**: Implement actual DuckDB data loading
2. **Enhance UI**: Add data visualization components
3. **Error Recovery**: Implement retry mechanisms
4. **Performance**: Optimize for large datasets
5. **Testing**: Add automated tests

## 🔄 Comparison with Original

| Aspect | Original (Broken) | Clean Version (Working) |
|--------|------------------|------------------------|
| Manifest | Complex, 150+ lines | Simple, 20 lines |
| JavaScript | Minified bundle | Clean, readable code |
| HTML | Evidence framework | Simple Domo structure |
| Status | ❌ "undefined is not a function" | ✅ Working perfectly |

## 📝 License

This project is part of the Evidence framework integration with Domo DDX platform.
