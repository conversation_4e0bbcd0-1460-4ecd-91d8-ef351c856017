# Project Cleanup Recommendations

## Summary
Your project has several redundant folders that can be safely removed to simplify the structure.

## KEEP These Folders (Essential):
- `evidence-project-fresh/` - Main Evidence project with DuckDB
- `dist/` - Webpack build output for DDX deployment  
- `node_modules/` (root) - Webpack dependencies
- Core files: `package.json`, `webpack.config.js`, `build-ddx.sh`, `Dockerfile`

## REMOVE These Folders (Redundant):
- `evidence-project/` - Older Evidence version (not used in build)
- `demo/` - Demo files (not used)
- `test-clean/` - Test files (duplicate of domo-evidence-clean)
- `domo-evidence-clean/` - Working version but superseded by webpack build
- `evidence_build/` - Temporary build copy (recreated during build)

## REMOVE These Files (Build Artifacts):
- `evidence-ddx-app.zip` - Can be regenerated
- `dist/manifest.json.backup` - Backup file

## Cleanup Commands:
```bash
# Remove redundant directories
rm -rf evidence-project/
rm -rf demo/
rm -rf test-clean/
rm -rf domo-evidence-clean/
rm -rf evidence_build/

# Remove build artifacts
rm -f evidence-ddx-app.zip
rm -f dist/manifest.json.backup

# Optional: Clean and rebuild to verify everything works
./build-ddx.sh
```

## After Cleanup Structure:
```
domo_app/
├── evidence-project-fresh/    # Main Evidence project
├── dist/                      # Build output
├── node_modules/              # Dependencies
├── build-ddx.sh              # Build script
├── webpack.config.js         # Webpack config
├── package.json              # Dependencies
├── Dockerfile                # Docker config
└── manifest.json             # Domo manifest
```

## Verification:
After cleanup, run `./build-ddx.sh` to ensure the build still works correctly.

## Space Savings:
This cleanup should reduce the project size significantly by removing:
- Duplicate Evidence projects (~500MB+ in node_modules)
- Demo and test files
- Build artifacts
