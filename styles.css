/* Evidence DuckDB Workflow - Clean Styles */

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  margin: 0;
  padding: 20px;
  background-color: #f8f9fa;
  color: #333;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
  color: #2563eb;
  margin-bottom: 10px;
  font-size: 2.5rem;
  font-weight: 700;
}

h2 {
  color: #374151;
  margin-top: 30px;
  margin-bottom: 15px;
  font-size: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

h3 {
  color: #4b5563;
  margin-top: 20px;
  margin-bottom: 10px;
  font-size: 1.25rem;
  font-weight: 600;
}

.status-section, .data-section {
  margin: 30px 0;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background-color: #f9fafb;
}

#status-display p {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  padding: 10px;
  border-radius: 4px;
  background-color: white;
}

#excel-data, #gov-data {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: white;
}

#excel-results, #gov-results {
  margin-top: 10px;
  padding: 10px;
  border-radius: 4px;
  background-color: #f3f4f6;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
}

pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 0.85rem;
  margin: 10px 0;
}

/* Status colors */
p[style*="color: green"] {
  background-color: #dcfce7 !important;
  border-left: 4px solid #16a34a;
}

p[style*="color: red"] {
  background-color: #fef2f2 !important;
  border-left: 4px solid #dc2626;
}

p[style*="color: orange"] {
  background-color: #fff7ed !important;
  border-left: 4px solid #ea580c;
}

p[style*="color: gray"] {
  background-color: #f3f4f6 !important;
  border-left: 4px solid #6b7280;
}

/* Responsive design */
@media (max-width: 768px) {
  body {
    padding: 10px;
  }
  
  .container {
    padding: 20px;
  }
  
  h1 {
    font-size: 2rem;
  }
  
  pre {
    font-size: 0.8rem;
    padding: 10px;
  }
}

/* Loading animation */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.loading {
  animation: pulse 2s infinite;
}
